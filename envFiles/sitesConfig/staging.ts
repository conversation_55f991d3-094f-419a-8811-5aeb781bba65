import DefaultSiteConfigs from './qa';

const collectionOnlySiteStores = [
  { qrCode: 'https://consumer-app.staging.drpt.io/qr/2000/47865046', name: 'r&d test store' },
];

const collectionAndDeliverySiteStores = [{ qrCode: 'XYZ789', name: 'Store 3' }];

// TODO: When starting to work on staging use the actual codes
const SiteConfigs = {
  CollectionOnlySite: {
    ...DefaultSiteConfigs.CollectionOnlySite,
    name: 'Serravalle Designer Outlet',
    packageCodePrefix: 'ITHA',
    stores: collectionOnlySiteStores,
  },

  CollectionAndDeliverySite: {
    ...DefaultSiteConfigs.CollectionAndDeliverySite,
    name: 'Cheshire Oaks',
    packageCodePrefix: 'UKHA',
    stores: collectionAndDeliverySiteStores,
  },

  BusinessDropSite: {
    ...DefaultSiteConfigs.BusinessDropSite,
    name: 'Bicester Village',
    packageCodePrefix: 'BVA',
    stores: [{ qrCode: 'https://consumer-app.staging.drpt.io/qr/25/dcec538e', name: '<PERSON><PERSON>' }],
  },

  WestgateOxford: {
    ...DefaultSiteConfigs.CollectionAndDeliverySite,
    name: 'Westgate Oxford',
    packageCodePrefix: 'WGOX',
    stores: [{ qrCode: 'https://consumer-app.staging.drpt.io/qr/useRealStoreCode', name: 'find store' }],
  },

  BelmontParkVillage: {
    ...DefaultSiteConfigs.CollectionAndDeliverySite,
    name: 'Belmont Park Village',
    packageCodePrefix: 'BMPK',
    stores: [{ qrCode: 'https://consumer-app.staging.drpt.io/qr/useRealStoreCode', name: 'find store' }],
  },

  GunwharfQuayes: {
    ...DefaultSiteConfigs.CollectionAndDeliverySite,
    name: 'Gunwharf Quays',
    packageCodePrefix: 'GUNW',
    stores: [{ qrCode: 'https://consumer-app.staging.drpt.io/qr/useRealStoreCode', name: 'find store' }],
  },

  BraintreeVillage: {
    ...DefaultSiteConfigs.CollectionAndDeliverySite,
    name: 'Braintree Village',
    packageCodePrefix: 'BRTV',
    stores: [{ qrCode: 'https://consumer-app.staging.drpt.io/qr/useRealStoreCode', name: 'find store' }],
  },
};

export default SiteConfigs;
