import Site from '../../testsInfra/entities/site';
import CurrencyCodesEnum from '../../testsInfra/enums/currencyCodesEnum';
import DropTypesEnum from '../../testsInfra/enums/dropTypesEnum';

const CollectionOnlySite: Site = {
  type: 0,
  allowedDropTypes: [DropTypesEnum.Collection],
  name: 'IT Handsfree',
  packageCodePrefix: 'ITHA',
  currencyCode: CurrencyCodesEnum.EURO,
  featureFlags: [],
  isWithHub: true,
  supportedAddressTypes: ['COLLECTION_POINT'],
  stores: [
    { qrCode: 'https://qa.drpt.app/qr/it-handsfree/7fae56ba', name: 'DROP<PERSON> TRAINING BUSINESS' },
    { qrCode: 'https://qa.drpt.app/qr/it-handsfree/2618ed7e', name: 'Super Mario Pizza' },
  ],
  amountOfCollectionPoints: 1,
};

const CollectionAndDeliverySite: Site = {
  type: 1,
  allowedDropTypes: [DropTypesEnum.Collection, DropTypesEnum.Delivery],
  name: 'UK Handsfree',
  packageCodePrefix: 'UKHA',
  currencyCode: CurrencyCodesEnum.GREAT_BRITISH_POUND,
  featureFlags: [],
  isWithHub: true,
  supportedAddressTypes: ['HOME', 'COLLECTION_POINT'],
  stores: [{ qrCode: 'https://qa.drpt.app/qr/uk-handsfree/3e79f832', name: 'Super Mario Pizza' }],
  amountOfCollectionPoints: 1,
};

const BusinessDropSite: Site = {
  type: 2,
  allowedDropTypes: [DropTypesEnum.Delivery],
  name: 'CA Smart Standalone',
  packageCodePrefix: 'CASA',
  currencyCode: CurrencyCodesEnum.CANADIAN_DOLLAR,
  featureFlags: [],
  isWithHub: false,
  supportedAddressTypes: ['HOME', 'HOTEL'],
  stores: [{ qrCode: 'https://qa.drpt.app/qr/ca-standalone/4c0b13f7', name: 'CA Good Karma Yoga (Wix)' }],
  amountOfCollectionPoints: 1,
};

const CollectionOnlySiteWithMultipleCollectionPoints: Site = {
  type: 0,
  allowedDropTypes: [DropTypesEnum.Collection],
  name: 'NL Handsfree',
  packageCodePrefix: 'NLHA',
  currencyCode: CurrencyCodesEnum.EURO,
  featureFlags: [],
  isWithHub: true,
  supportedAddressTypes: ['COLLECTION_POINT'],
  stores: [{ qrCode: 'https://qa.drpt.app/qr/nl-handsfree/ea54af67', name: 'DROPIT TRAINING BUSINESS' }],
  amountOfCollectionPoints: 2,
};

const DeliveryOnlySite: Site = {
  type: 1,
  allowedDropTypes: [DropTypesEnum.Collection, DropTypesEnum.Delivery],
  name: 'US Handsfree + Direct',
  packageCodePrefix: 'USHA',
  currencyCode: CurrencyCodesEnum.AMERICAN_DOLLAR,
  featureFlags: [],
  isWithHub: true,
  supportedAddressTypes: ['HOME', 'HOTEL'],
  stores: [
    { qrCode: 'https://qa.drpt.app/qr/us-handsfree-plus-direct-sourcing/54900ef5', name: 'DROPIT TRAINING BUSINESS' },
  ],
  amountOfCollectionPoints: 1,
};

export default {
  CollectionOnlySite,
  CollectionAndDeliverySite,
  BusinessDropSite,
  CollectionOnlySiteWithMultipleCollectionPoints,
  DeliveryOnlySite,
};
