import { expect } from '@playwright/test';
import Package from '../../../entities/package';
import Site from '../../../entities/site';
import DeliveryStatusEnum from '../../../enums/deliveryStatusEnum';
import { TestStep } from '../../decorators';
import { AdminContext } from '../../interfaces/adminContext';
import { Auth0LoginUser } from '../../interfaces/auth0Login';
import { Logger } from '../../logger';
import OperationHubPage from '../../pages/businessPages/hfs/operationsHub';
import NavigationBusinessPage from '../../pages/businessPages/navigationBusinessPage';
import BaseAction from '../baseAction';

export default class DeliveryDetailsActions extends BaseAction<AdminContext & Auth0LoginUser> {
  constructor(actor: AdminContext & Auth0LoginUser) {
    super(actor);
  }

  @TestStep(`Rebook delivery`)
  public async rebookDelivery({
    site,
    packagee,
    shouldGoToOperationHubAfterAction = true,
    shouldNavigateToOperationHub = false,
    shouldSoftAssert = false,
  }: {
    site: Site;
    packagee: Package;
    shouldGoToOperationHubAfterAction?: boolean;
    shouldNavigateToOperationHub?: boolean;
    shouldSoftAssert?: boolean;
  }) {
    const detailsPage = await this.goToPackageDetails({
      site,
      packagee,
      shouldSwitchToNewPage: !shouldGoToOperationHubAfterAction,
      shouldNavigateToPage: shouldNavigateToOperationHub,
    });

    await detailsPage.threeDotButton.selectOption({ optionToSelect: 'Rebook delivery' });
    const firstCourier = await detailsPage.rebookModal.couriersOptions.firstResult();
    await firstCourier.click();
    await detailsPage.rebookModal.rebookDeliveryButton.click();

    if (shouldSoftAssert) {
      await expect(
        detailsPage.page.getByText('Rebook initiated: Courier:'),
        'Asserting rebook note was added',
      ).toBeVisible({
        timeout: 10000,
      });
    } else {
      await expect
        .soft(detailsPage.page.getByText('Rebook initiated: Courier:'), 'Asserting rebook note was added')
        .toBeVisible({
          timeout: 10000,
        });
    }

    await this.waitForServicesToSync(detailsPage);

    if (shouldGoToOperationHubAfterAction) {
      await this.goToOperationHubAfterOpeningDeliveryPage();
    }
  }

  @TestStep(`Marks delivery as delivered`)
  public async markDeliveryAsDelivered({
    site,
    packagee,
    shouldGoToOperationHubAfterAction = true,
  }: {
    site: Site;
    packagee: Package;
    shouldGoToOperationHubAfterAction?: boolean;
  }) {
    const detailsPage = await this.goToPackageDetails({
      site,
      packagee,
      shouldSwitchToNewPage: !shouldGoToOperationHubAfterAction,
    });

    await detailsPage.threeDotButton.selectOption({ optionToSelect: 'Mark as delivered' });
    await detailsPage.markAsDeliveredButton.click();

    await this.waitForServicesToSync(detailsPage);

    if (shouldGoToOperationHubAfterAction) {
      await this.goToOperationHubAfterOpeningDeliveryPage();
    }
  }

  @TestStep(`Seal package`)
  public async sealPackage({
    site,
    packagee,
    shouldGoToOperationHubAfterAction = true,
  }: {
    site: Site;
    packagee: Package;
    shouldGoToOperationHubAfterAction?: boolean;
  }) {
    const detailsPage = await this.goToPackageDetails({
      site,
      packagee,
      shouldSwitchToNewPage: !shouldGoToOperationHubAfterAction,
    });
    await detailsPage.threeDotButton.selectOption({ optionToSelect: 'Seal delivery' });
    await detailsPage.sealButton.click();

    if (shouldGoToOperationHubAfterAction) {
      await this.goToOperationHubAfterOpeningDeliveryPage();
    }
  }

  private async goToPackageDetails({
    site,
    packagee,
    shouldSwitchToNewPage = true,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    packagee: Package;
    shouldSwitchToNewPage?: boolean;
    shouldNavigateToPage?: boolean;
  }) {
    if (shouldNavigateToPage) {
      await this.actor.perform.goToOperationsHubForTheSite({ site, afterLogin: true });
    }

    const operationHubPage = new OperationHubPage(this.actor.page, site);

    await operationHubPage.searchPackageInput.fill(packagee.packageCode);
    const detailsPopup = operationHubPage.page.waitForEvent('popup');
    await operationHubPage.packageInfoNewTabButton.click();

    const newDetailsPage = new OperationHubPage(await detailsPopup, site);
    await newDetailsPage.packageStatus.click();

    if (shouldSwitchToNewPage) {
      this.actor.page = newDetailsPage.page;
    }

    Logger.info(`Delivery details url: ${newDetailsPage.getCurrentUrl()}`, newDetailsPage.getCurrentUrl());
    return newDetailsPage;
  }

  @TestStep(`Admin checks courier status`)
  public async whatIsTheCourierStatus({
    site,
    packagee,
    shouldNavigateToPage = true,
    shouldRefreshPage = true,
  }: {
    site: Site;
    packagee: Package;
    shouldNavigateToPage?: boolean;
    shouldRefreshPage?: boolean;
  }) {
    if (!shouldNavigateToPage) {
      const newDetailsPage = new OperationHubPage(this.actor.page, site);

      await newDetailsPage.deliverySuccessfullyUpdatedToast.waitToBeVisible();
      await expect(newDetailsPage.deliverySuccessfullyUpdatedToast.locator).toBeHidden({ timeout: 10000 });

      if (shouldRefreshPage) {
        await newDetailsPage.refreshPage();
      }

      return await newDetailsPage.courierStatus.textContent();
    }

    const newDetailsPage = await this.goToPackageDetails({ site, packagee });
    const courierStatus = await newDetailsPage.courierStatus.textContent({ shouldClick: false });

    Logger.debug(`Courier status: ${courierStatus}`);

    return courierStatus;
  }

  @TestStep(`Admin checks delivery status`)
  public async whatIsTheDeliveryStatus({
    site,
    packagee,
    shouldNavigateToPage = true,
  }: {
    site: Site;
    packagee: Package;
    shouldNavigateToPage?: boolean;
  }) {
    if (!shouldNavigateToPage) {
      const newDetailsPage = new OperationHubPage(this.actor.page, site);

      await newDetailsPage.deliverySuccessfullyUpdatedToast.waitToBeVisible();
      await expect(newDetailsPage.deliverySuccessfullyUpdatedToast.locator).toBeHidden({ timeout: 10000 });

      await newDetailsPage.refreshPage();
      return await newDetailsPage.packageStatus.textContent();
    }

    const newDetailsPage = await this.goToPackageDetails({ site, packagee });
    return await newDetailsPage.packageStatus.textContent();
  }

  @TestStep(`Deliver packages`)
  public async deliverPackages({
    site,
    packagee,
    isLoggedIn = true,
    shouldGoToOperationHub = false,
    shouldGoToOperationHubAfterAction = false,
  }: {
    site: Site;
    packagee: Package;
    isLoggedIn?: boolean;
    shouldGoToOperationHub?: boolean;
    shouldGoToOperationHubAfterAction?: boolean;
  }) {
    if (!isLoggedIn) {
      await this.actor.perform.login();
    }

    if (shouldGoToOperationHub) {
      await this.actor.perform.goToOperationsHubForTheSite({ site, afterLogin: isLoggedIn });
    }

    await this.markDeliveryAsDelivered({
      site,
      packagee,
      shouldGoToOperationHubAfterAction,
    });
  }

  @TestStep('Assert All Options Are Disabled')
  private async assertAllOptionsAreDisabled({ detailsPage }: { detailsPage: OperationHubPage }) {
    const allOptions = await detailsPage.threeDotButton.getOptions({ shouldOpenOptions: true });

    for (const option of allOptions) {
      expect.soft(await option.isDisabled).toBeTruthy();
    }
  }

  @TestStep('Assert Courier Status')
  public async assertCourierStatus({
    packagee,
    site,
    shouldNavigateToPage = true,
    shouldGoToOperationHubAfterAction = true,
    shouldRefreshPage = true,
    assertion,
  }: {
    packagee: Package;
    site: Site;
    shouldNavigateToPage?: boolean;
    shouldGoToOperationHubAfterAction?: boolean;
    shouldRefreshPage?: boolean;
    assertion: (status: string) => void;
  }) {
    const courierCurrentStatus = await this.whatIsTheCourierStatus({
      site,
      packagee,
      shouldNavigateToPage,
      shouldRefreshPage,
    });

    assertion(courierCurrentStatus);

    if (shouldGoToOperationHubAfterAction) {
      await this.actor.goTo.operationsHubPage({ site, afterLogin: false });
    }
  }

  @TestStep('Assert delivery status')
  public async assertDeliveryStatus({
    packagee,
    site,
    shouldNavigateToPage = true,
    assertion,
  }: {
    packagee: Package;
    site: Site;
    shouldNavigateToPage?: boolean;
    assertion: (status: string) => void;
  }) {
    const deliveryCurrentStatus = await this.whatIsTheDeliveryStatus({
      site,
      packagee,
      shouldNavigateToPage,
    });

    if (!deliveryCurrentStatus) {
      throw new Error(`Failed to get delivery status`);
    }

    assertion(deliveryCurrentStatus);

    if (deliveryCurrentStatus === DeliveryStatusEnum.Delivered) {
      const detailsPage = new OperationHubPage(this.actor.page, site);

      await this.assertAllOptionsAreDisabled({ detailsPage });
      await this.assertCourierStatus({
        packagee,
        site,
        shouldNavigateToPage: false,
        shouldGoToOperationHubAfterAction: false,
        shouldRefreshPage: false,
        assertion: (status) =>
          expect(status, { message: 'Asserting courier status' }).toBe(DeliveryStatusEnum.Delivered),
      });
    }
  }

  private async goToOperationHubAfterOpeningDeliveryPage() {
    await this.actor.perform.bringPageToFront();

    const navigationBusinessPage = new NavigationBusinessPage(this.actor.page);
    await this.actor.perform.refreshPage(navigationBusinessPage);
  }

  private async waitForServicesToSync(detailsPage: OperationHubPage) {
    await detailsPage.deliverySuccessfullyUpdatedToast.waitToBeVisible();
    await expect(detailsPage.deliverySuccessfullyUpdatedToast.locator).toBeHidden({ timeout: 10000 });
  }
}
