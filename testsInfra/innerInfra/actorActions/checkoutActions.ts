import { expect } from '@playwright/test';
import DataManager from '../../dataManager';
import Package from '../../entities/package';
import DropTypesEnum from '../../enums/dropTypesEnum';
import ViewportTypeEnum from '../../enums/viewPortTypeEnum';
import { TestStep } from '../decorators';
import { BusinessOwnerContext } from '../interfaces/businessOwnerContext';
import { CheckoutPage } from '../interfaces/checkoutPage';
import { ConsumerContext } from '../interfaces/consumerContext';
import { DroppingUser } from '../interfaces/droppingUser';
import { Logger } from '../logger';
import CheckoutConsumerPage from '../pages/consumerPages/checkoutConsumerPage';
import Button from '../pages/elements/button';
import BaseAction from './baseAction';

export default abstract class CheckoutActions<
  T extends (ConsumerContext | BusinessOwnerContext) & DroppingUser,
> extends BaseAction<T> {
  constructor(actor: T) {
    super(actor);
  }

  @TestStep('What is the package input scan error')
  public async whatIsThePackageInputScanError(checkoutPage?: CheckoutPage) {
    if (checkoutPage) {
      await checkoutPage.scanPackagesInputText.waitToBeVisible();

      return checkoutPage.scanPackageInputError.textContent({ shouldClick: false });
    }

    throw new Error('checkout page is undefined');
  }

  @TestStep(`Add package(s) to the drop`)
  public async addPackages({
    additionalPackages,
    checkoutPage = new CheckoutConsumerPage(this.actor.page),
    shouldBarcodeInputErrorAppear = false,
    shouldNavigateToPage = true,
    shouldUseImages = false,
  }: {
    additionalPackages: Package[];
    checkoutPage?: CheckoutPage;
    shouldBarcodeInputErrorAppear?: boolean;
    shouldNavigateToPage?: boolean;
    shouldUseImages?: boolean;
  }) {
    // We are assuming that if the user did not provide any packages
    // They want to drop a package so we generate the data for them
    // There is no reason to be in this page without trying to add a package, valid or otherwise
    if (additionalPackages.length === 0) {
      additionalPackages = DataManager.DataGenerator.generatePackages(this.actor.site, 1);
    }

    if (shouldNavigateToPage) {
      await checkoutPage.packagesCardButton.click();

      if (this.actor.get.viewPortType === ViewportTypeEnum.Mobile) {
        if (!shouldUseImages) {
          await checkoutPage.pageNewPackageScannerModalCloseButton.click();
        }
      }
    }

    let wasAPackageAddedToTheDrop = false;

    for (const i in additionalPackages) {
      const packagee = additionalPackages[i];
      let wasPackageAdded = false;

      if (shouldUseImages) {
        wasPackageAdded = await checkoutPage.packageScannerModalDeleteButton(i).waitToBeVisible({
          shouldThrowError: !shouldBarcodeInputErrorAppear,
          errorMessage: 'Failed to add package, package delete button did not appear',
        });
      } else {
        await checkoutPage.scanPackagesInputText.fill(packagee.packageCode);
        wasPackageAdded = await checkoutPage.packageDeleteButton(i).waitToBeVisible({
          shouldThrowError: !shouldBarcodeInputErrorAppear,
          errorMessage: 'Failed to add package, package delete button did not appear',
        });
      }

      if (shouldBarcodeInputErrorAppear) {
        expect(wasPackageAdded, 'Asserting if the package addition failed as expected').toBeFalsy();
        Logger.info(
          `Package validation for ${packagee.packageCode} works as expected, package wasn't added to the drop`,
        );

        continue;
      }

      wasAPackageAddedToTheDrop = true;
      Logger.info(`Added package ${packagee.packageCode} to the drop`, packagee.packageCode);
    }

    if (shouldUseImages) {
      await checkoutPage.packageScannerModalSaveButton.click();
    } else {
      await checkoutPage.packageScannerManualSaveButton.click();
    }

    if (!wasAPackageAddedToTheDrop) {
      const didErrorPopupAppear = await checkoutPage.packageScannerSaveErrorPopup.waitToBeVisible({
        shouldThrowError: false,
        errorMessage:
          "Error popup for empty drop didn't appear, retrying to make it appear by reclicking the save button",
      });

      if (!didErrorPopupAppear) {
        await checkoutPage.packageScannerManualSaveButton.click();
        await checkoutPage.packageScannerSaveErrorPopup.waitToBeVisible({
          shouldThrowError: true,
          errorMessage: "Error popup for empty drop didn't appear",
        });
      }

      expect(await checkoutPage.packageScannerSaveErrorPopup.textContent({ shouldClick: false })).toContain(
        `Please connect bags to the delivery before clicking 'Save'`,
      );

      // We click on the popup to remove it and not interrupt other tests
      await checkoutPage.packageScannerSaveErrorPopup.click({ trial: false });
    }

    return wasAPackageAddedToTheDrop ? additionalPackages : [];
  }

  @TestStep(`Add receipts with image`)
  public async addReceiptsWithImage({ checkoutPage }: { checkoutPage: CheckoutPage }) {
    if (this.actor.get.viewPortType !== ViewportTypeEnum.Mobile) {
      throw new Error('Adding receipts with image is not supported on non mobile ports');
    }

    await checkoutPage.receiptsCardButton.click();
    if (checkoutPage instanceof CheckoutConsumerPage) {
      const consumerCheckoutPage = checkoutPage;
      if ((this.actor as ConsumerContext).get.isFirstDrop) {
        await consumerCheckoutPage.receiptAnimationConfirmationButton.click();
      }
    }

    // Playwright might click the button before the button actually can do anything
    // so we click it again if the expected action didn't happen
    await checkoutPage.receiptsCaptureImageButton.click({
      reclickCondition: async () => {
        return !(await checkoutPage.saveReceiptButton.waitToBeVisible());
      },
    });

    await checkoutPage.saveReceiptButton.click({
      reclickCondition: async () => {
        return !(await checkoutPage.saveReceiptButton.waitToBeInvisible());
      },
    });

    await checkoutPage.saveReceiptButton.waitToBeInvisible({
      shouldThrowError: true,
      errorMessage: 'Failed to add image receipt to drop, save button is still visible',
    });
  }

  @TestStep(`Add receipts manually`)
  public async addReceiptsManually({ money = '50', checkoutPage }: { money?: string; checkoutPage: CheckoutPage }) {
    await checkoutPage.receiptsCardButton.click();

    Logger.info(`User's viewport: ${this.actor.get.viewPortType}`);
    Logger.info(`Checkout page used: ${checkoutPage.constructor.name}`);
    const shouldPayForDropitPass = (this.actor as ConsumerContext).get.isFirstDrop;
    Logger.info(
      `${shouldPayForDropitPass === undefined ? 'No need to pay for dropit pass in fulfillment app' : `Does consumer drop for the first time? (need to pay for dropitpass): ${shouldPayForDropitPass}`}`,
    );

    if (checkoutPage instanceof CheckoutConsumerPage && shouldPayForDropitPass) {
      await checkoutPage.closeAnimationButton.click();
    } else if (this.actor.get.viewPortType === ViewportTypeEnum.Mobile) {
      await checkoutPage.receiptsCameraModalCloseButton.click();
    }

    await checkoutPage.receiptsManualMoneyTextInput.fill(money);
    await checkoutPage.deliveryOptionsSaveButton.click();
  }

  public abstract dropPackages(args: unknown);

  @TestStep(`Select Dropit pass`)
  public async selectDropitPass({
    checkoutPage,
    dropitPass: dropitPassButton,
  }: {
    checkoutPage: CheckoutPage;
    dropitPass?: Button;
  }) {
    await checkoutPage.deliveryOptionsMenuOptionButton.click();

    if (dropitPassButton) {
      await dropitPassButton.click();
    } else {
      await checkoutPage.defaultDeliveryPassOption.click();
    }

    await checkoutPage.deliveryOptionsSaveButton.click();
  }

  protected abstract goToAddressPage(checkoutPage: CheckoutPage): Promise<void>;

  @TestStep(`Add address`)
  public async addAddress({
    address,
    additionalContactInfo,
    checkoutPage,
    shouldClickOnSave = true,
    shouldThrowError = true,
  }: {
    address: string;
    additionalContactInfo?: string;
    checkoutPage: CheckoutPage;
    shouldClickOnSave?: boolean;
    shouldThrowError?: boolean;
  }) {
    await this.goToAddressPage(checkoutPage);
    let addressFound: boolean = false;

    if (this.actor.dropType === DropTypesEnum.Delivery) {
      addressFound = await checkoutPage.addressSearchInput.selectOption({
        optionToSelect: address,
        timeoutAfterAction: 2000,
        shouldThrowError,
      });

      const isAddressAHotel = await checkoutPage.hotelRoomNumber.waitToBeVisible({
        shouldThrowError: false,
        timeout: 2000,
      });

      if (isAddressAHotel) {
        await checkoutPage.hotelRoomNumber.fill('123');
      }

      if (additionalContactInfo) {
        await checkoutPage.contactInfoNotesTextInput.fill(additionalContactInfo);
      }
    }

    if (shouldClickOnSave) {
      await checkoutPage.addressSaveButton.click({ shouldScrollIntoView: true });
    }

    return addressFound;
  }
}
