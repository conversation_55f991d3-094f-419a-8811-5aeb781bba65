import { expect } from '@playwright/test';
import { TestStep } from '../decorators';
import { AdminContext } from '../interfaces/adminContext';
import { Auth0LoginUser } from '../interfaces/auth0Login';
import { BusinessOwnerContext } from '../interfaces/businessOwnerContext';
import { Logger } from '../logger';
import Auth0LoginPage from '../pages/businessPages/loginBusinessPage';
import BaseAction from './baseAction';

export default class LoginActions<
  T extends Auth0LoginUser & (AdminContext | BusinessOwnerContext),
> extends BaseAction<T> {
  constructor(actor: T) {
    super(actor);
  }

  @TestStep(`Go to login page`)
  public async goToLoginPage() {
    return await new Auth0LoginPage(this.actor.page, this.actor.loginType).navigate();
  }

  @TestStep(`Login`)
  public async login({
    email = this.actor.email,
    password = this.actor.password,
    submit = true,
    shouldOpenNewTab = false,
    shouldNavigateToPage = true,
    expectLoginToSucceed = true,
  }: {
    email?: string;
    password?: string;
    submit?: boolean;
    shouldOpenNewTab?: boolean;
    shouldNavigateToPage?: boolean;
    expectLoginToSucceed?: boolean;
  } = {}) {
    if (shouldOpenNewTab) {
      this.actor.page = await this.actor.page.context().newPage();
    }

    const loginPage = new Auth0LoginPage(this.actor.page, this.actor.loginType);
    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: loginPage,
      shouldWaitForLoad: false,
    });

    Logger.info(`Inputting login Credentials ${submit ? 'and submitting' : ''}`);

    await loginPage.emailInput.fill(email);
    await loginPage.passwordInput.fill(password);

    if (submit) {
      await loginPage.continueButton.click();

      if (expectLoginToSucceed) {
        await loginPage.emailInput.waitToBeInvisible({
          timeout: 5000,
          shouldThrowError: true,
          errorMessage: `Login failed for email: ${email}.\nIf this causes all Admin and Fulfillment tests to fail, it's likely a credential issue. If not, the cause may be an Auth0 error or a bug.`,
        });
      }
    }
  }

  @TestStep(`Get login error message`)
  public async whatIsTheLoginErrorMessage({ shouldCleanTextContent = true } = {}) {
    const loginPage = new Auth0LoginPage(this.actor.page, this.actor.loginType);
    const didErrorMessageAppear = await loginPage.errorMessage.waitToBeVisible();

    return didErrorMessageAppear ? await loginPage.errorMessage.textContent({ shouldCleanTextContent }) : '';
  }

  @TestStep(`Get empty field error popup message`)
  public async getErrorPopupMessageForEmptyField(field: 'email' | 'password') {
    const loginPage = new Auth0LoginPage(this.actor.page, this.actor.loginType);
    const validationState = await loginPage.getEmptyFieldValidationState(field);

    expect.soft(validationState.isValid, `No validation popup appeared for empty ${field}`).toBeFalsy();
    expect.soft(validationState.isValueMissing, `Validation popup was not triggered due to empty field`).toBeTruthy();

    return validationState.message;
  }

  @TestStep(`Assert invalid login inputs`)
  public async assertInvalidLoginInputs(
    invalidCredentials: Array<{ email: string; password: string }>,
    assertion: (errorMessage: string, credentials: { email: string; password: string }) => void,
  ) {
    for (const credentials of invalidCredentials) {
      await this.actor.perform.login({
        email: credentials.email,
        password: credentials.password,
        shouldNavigateToPage: false,
        expectLoginToSucceed: false,
      });

      const errorMessage = await this.actor.ask.whatIsTheLoginErrorMessage();
      assertion(errorMessage, credentials);
    }
  }
}
