import { expect, Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import CreditCard from '../../../entities/creditCard';
import Package from '../../../entities/package';
import DropTypesEnum from '../../../enums/dropTypesEnum';
import { TestStep } from '../../decorators';
import { ConsumerContext } from '../../interfaces/consumerContext';
import { DroppingUser } from '../../interfaces/droppingUser';
import { Store } from '../../interfaces/store';
import { Logger } from '../../logger';
import CheckoutConsumerPage from '../../pages/consumerPages/checkoutConsumerPage';
import StripePage from '../../pages/stripePage';
import CheckoutActions from '../checkoutActions';

export default class ConsumerCheckoutActions extends CheckoutActions<ConsumerContext & DroppingUser> {
  constructor(actor: ConsumerContext & DroppingUser) {
    super(actor);
  }

  public async whatIsThePaymentErrorText() {
    const checkoutConsumerPage = new CheckoutConsumerPage(this.actor.page);

    return checkoutConsumerPage.failedToDropErrorModal.textContent();
  }

  public override whatIsThePackageInputScanError(
    checkoutPage: CheckoutConsumerPage = new CheckoutConsumerPage(this.actor.page),
  ) {
    return super.whatIsThePackageInputScanError(checkoutPage);
  }

  @TestStep(`Add package(s) to the drop`)
  public async addPackagesToDrop({
    additionalPackages,
    checkoutConsumerPage,
    shouldPackageAdditionSucceed = true,
    shouldNavigateToPage = true,
    shouldUseImages = false,
  }: {
    additionalPackages: Package[];
    checkoutConsumerPage: CheckoutConsumerPage;
    shouldPackageAdditionSucceed?: boolean;
    shouldNavigateToPage?: boolean;
    shouldUseImages?: boolean;
  }) {
    const addedPackages = await super.addPackages({
      additionalPackages,
      checkoutPage: checkoutConsumerPage,
      shouldBarcodeInputErrorAppear: !shouldPackageAdditionSucceed,
      shouldNavigateToPage,
      shouldUseImages,
    });

    if (addedPackages.length > 0) {
      this.actor.set.addPackagesToConsumer(addedPackages);
    }

    return addedPackages;
  }

  @TestStep(`Add promo code`)
  public async addPromoCode({
    promoCode = '1234',
    checkoutConsumerPage,
  }: {
    promoCode?: string;
    checkoutConsumerPage: CheckoutConsumerPage;
  }) {
    await checkoutConsumerPage.promoCodeButton.click();
    await checkoutConsumerPage.promoCodeInput.fill(promoCode);
    await checkoutConsumerPage.promoCodeApplyButton.click();
  }

  @TestStep('Fill Payment details in Stripe')
  public async fillPaymentDetails(creditCard: CreditCard, expectedAmountToPay: number, failPayment: boolean) {
    if (expectedAmountToPay <= 0) {
      throw new Error('Expected amount to pay cannot be 0 or less');
    }

    const stripePage = await new StripePage(this.actor.page);

    await stripePage.waitForLoad();

    const expectedPayment = DataManager.DataConverter.convertNumberToPaymentString({
      amount: expectedAmountToPay,
      currencyCode: this.actor.site.currencyCode,
      languageLocal: await this.actor.ask.whatIsPageLanguage(),
    });

    // Stripe may show local currency, and add an option which payment we want
    // This way we make sure we constantly click on the correct payment option
    const totalPaymentAmountElement = await stripePage.totalPaymentAmount(expectedPayment);
    await totalPaymentAmountElement.waitToBeVisible({ shouldThrowError: true });
    await totalPaymentAmountElement.click();

    if (failPayment) {
      await stripePage.backToDropitButton.click();

      return;
    }

    if (await stripePage.cardDetailsExpandButton.waitToBeVisible()) {
      await stripePage.cardDetailsExpandButton.click();
    }

    await stripePage.cardNumberInput.fill(creditCard.number);
    await stripePage.cardExpiryInput.fill(creditCard.expiryDate);
    await stripePage.cardCvcInput.fill(creditCard.cvv);
    try {
      let linkAccountPopup: Page | null = null;
      const context = this.actor.page.context();
      const linkAccountPopupPromise = context.waitForEvent('page', { timeout: 2000 });

      await stripePage.billingName.fill(creditCard.holderName);

      linkAccountPopup = await linkAccountPopupPromise;
      await linkAccountPopup.waitForLoadState();
      await linkAccountPopup.close();
    } catch (e) {
      Logger.debug(e.message);
      Logger.info("Popup didn't appear/didn't close trying to continue stripe flow");
    }

    const billingPostalCodeInput = await stripePage.GetBillingPostalCodeInput();
    if (billingPostalCodeInput) {
      await billingPostalCodeInput.fill(creditCard.postalCode);
    }

    await stripePage.submitButton.click();
  }

  @TestStep(`Check out`)
  public async checkout(checkoutConsumerPage?: CheckoutConsumerPage, expectedAmountToPay = 0, failDrop = false) {
    checkoutConsumerPage = checkoutConsumerPage ?? new CheckoutConsumerPage(this.actor.page);

    await checkoutConsumerPage.checkoutButton.click();

    if (expectedAmountToPay > 0 && this.actor.get.isFirstDrop) {
      if (!this.actor.get.creditCard) {
        const paymentFailVarName = Object.keys({ failPayment: failDrop })[0];

        throw new Error(
          `Credit card is required for checkout, if you want to fail the payment, set the '${paymentFailVarName}' argument to true`,
        );
      }

      await this.fillPaymentDetails(this.actor.get.creditCard, expectedAmountToPay, failDrop);
    }

    if (failDrop) {
      await checkoutConsumerPage.waitForLoad();
      await expect(checkoutConsumerPage.failedToDropErrorModal.locator).toBeVisible({
        timeout: DataManager.Consts.LONG_ACTION_TIMEOUT,
      });

      return;
    }

    switch (this.actor.dropType) {
      case DropTypesEnum.Collection:
        await checkoutConsumerPage.confirmCheckoutOkButton.click();
        await this.confirmCutoffTimePopup({ checkoutConsumerPage, expectedToBeVisible: false });
        break;
      case DropTypesEnum.Delivery:
        await checkoutConsumerPage.deliveryBookedButton.click();
        break;
      default:
        throw new Error("The Automation doesn't support that drop type yet");
    }

    this.actor.set.consumerTo_Not_PayForNextDropPass();
  }

  protected async goToAddressPage(checkoutPage: CheckoutConsumerPage) {
    await checkoutPage.addressMenuOptionButton.click();

    if (this.actor.site.allowedDropTypes.length > 1) {
      await this.selectDeliveryMethod({
        checkoutConsumerPage: checkoutPage as CheckoutConsumerPage,
        shouldNavigateToPage: false,
      });
    }
  }

  @TestStep(`Add address`)
  public async addAddress({
    address,
    additionalContactInfo,
    checkoutConsumerPage,
    shouldClickOnSave = true,
    shouldThrowError = true,
  }: {
    address: string;
    additionalContactInfo?: string;
    checkoutConsumerPage?: CheckoutConsumerPage;
    shouldClickOnSave?: boolean;
    shouldThrowError?: boolean;
  }) {
    checkoutConsumerPage = checkoutConsumerPage ?? new CheckoutConsumerPage(this.actor.page);

    const addressFound = await super.addAddress({
      address,
      additionalContactInfo,
      checkoutPage: checkoutConsumerPage,
      shouldClickOnSave,
      shouldThrowError,
    });

    if (shouldClickOnSave) {
      await this.confirmCutoffTimePopup({
        checkoutConsumerPage: checkoutConsumerPage ?? new CheckoutConsumerPage(this.actor.page),
        expectedToBeVisible: this.actor.dropType !== DropTypesEnum.Delivery,
      });
    }

    return addressFound;
  }

  @TestStep(`Select delivery method`)
  public async selectDeliveryMethod({
    checkoutConsumerPage,
    shouldNavigateToPage = false,
  }: {
    checkoutConsumerPage?: CheckoutConsumerPage;
    shouldNavigateToPage?: boolean;
  }) {
    checkoutConsumerPage = checkoutConsumerPage ?? new CheckoutConsumerPage(this.actor.page);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: checkoutConsumerPage });

    switch (this.actor.dropType) {
      case DropTypesEnum.Collection:
        await checkoutConsumerPage.collectionTypeModalButton.click();

        break;

      case DropTypesEnum.Delivery:
        await checkoutConsumerPage.deliveryTypeModalButton.click();

        break;

      default:
        throw new Error("The Automation doesn't support that drop type yet");
    }
  }
  @TestStep('Select Collection Point')
  public async selectCollectionPoint({ collectionPointName }: { collectionPointName?: string } = {}) {
    const checkoutConsumerPage = new CheckoutConsumerPage(this.actor.page);
    await checkoutConsumerPage.collectionPointsOnSiteMenuButton.click();

    if (this.actor.site.amountOfCollectionPoints === 1) {
      throw new Error('Site does not have multiple collection points');
    }

    if (collectionPointName) {
      await checkoutConsumerPage.collectionPointsOptions.selectOption({ optionToSelect: collectionPointName });
    } else {
      await checkoutConsumerPage.collectionPointsOptions.selectFirstOption();
    }

    await checkoutConsumerPage.addressSaveButton.click();

    await this.confirmCutoffTimePopup({
      checkoutConsumerPage,
      expectedToBeVisible: true,
    });
  }

  @TestStep(`Select collection type`)
  public async selectCollectionType({
    dropType,
    doesSiteAllowDelivery,
    shouldNavigateToPage = true,
  }: {
    dropType: DropTypesEnum;
    doesSiteAllowDelivery: boolean;
    shouldNavigateToPage: boolean;
  }) {
    const checkoutConsumerPage = new CheckoutConsumerPage(this.actor.page);

    if (!this.actor.site.allowedDropTypes.some((allowedDropType) => allowedDropType === dropType)) {
      throw new Error('Site does not allow that type of drop');
    }

    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: checkoutConsumerPage });
    if (!doesSiteAllowDelivery) {
      await checkoutConsumerPage.gotItButton.click();
    } else {
      switch (dropType) {
        case DropTypesEnum.Collection:
          await checkoutConsumerPage.collectionTypeModalButton.click();
          break;

        case DropTypesEnum.Delivery:
          await checkoutConsumerPage.deliveryTypeModalButton.click();
          break;

        default:
          throw new Error("The Automation doesn't support that drop type yet");
      }
    }

    return checkoutConsumerPage;
  }

  @TestStep('Assert Drop page is rendered')
  public async goToCheckoutPage({
    shouldNavigateToPage = true,
    shouldNavigateThroughUI = true,
    store = this.actor.site.stores[0],
    shouldScanQRCodeThroughApp = true,
    shouldUseImages = false,
  }: {
    shouldNavigateToPage?: boolean;
    shouldNavigateThroughUI?: boolean;
    store?: Store;
    shouldScanQRCodeThroughApp?: boolean;
    shouldUseImages?: boolean;
  } = {}) {
    let checkoutConsumerPage = new CheckoutConsumerPage(this.actor.page);
    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: checkoutConsumerPage,
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        if (shouldScanQRCodeThroughApp) {
          await this.actor.perform.scanStoreQRCodeThroughApp({ store, shouldUseImages });
        }

        if (!shouldUseImages) {
          checkoutConsumerPage = await this.actor.perform.pressStartDropButton();
        }
      },
    });

    await this.confirmCutoffTimePopup({
      checkoutConsumerPage,
      expectedToBeVisible: this.actor.site.allowedDropTypes.length < 2 && this.actor.site.amountOfCollectionPoints < 2,
    });

    await checkoutConsumerPage.checkoutButton.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      timeout: 5000,
      errorMessage: 'Failed to load drop page, checkout button is not visible',
    });

    return checkoutConsumerPage;
  }

  @TestStep(`Drops package(s)`)
  public async dropPackages({
    shouldNavigateToPage = false,
    shouldNavigateThroughUI = true,
    packages = [],
    promoCode = '1234',
    money = '50',
    additionalContactInfo = 'Add a nice message to it',
    expectedAmountToPay = 0,
    failDrop = false,
    shouldPackageAdditionSucceed = true,
    shouldScanQRCodeThroughApp = true,
    shouldUseImages = false,
    shouldCheckout = false,
    shouldAssertBeforeCheckout = true,
  }: {
    shouldNavigateToPage?: boolean;
    shouldNavigateThroughUI?: boolean;
    packages?: Package[];
    promoCode?: string;
    money?: string;
    additionalContactInfo?: string;
    expectedAmountToPay?: number;
    failDrop?: boolean;
    shouldPackageAdditionSucceed?: boolean;
    shouldScanQRCodeThroughApp?: boolean;
    shouldUseImages?: boolean;
    shouldCheckout?: boolean;
    shouldAssertBeforeCheckout?: boolean;
  } = {}) {
    const doesSiteAllowDelivery = this.actor.site.allowedDropTypes.some(
      (dropType) => dropType === DropTypesEnum.Delivery,
    );

    const checkoutConsumerPage = await this.goToCheckoutPage({
      shouldNavigateToPage,
      shouldNavigateThroughUI,
      shouldScanQRCodeThroughApp,
      shouldUseImages,
    });

    if (this.actor.get.isFirstDrop) {
      if (promoCode) {
        await this.addPromoCode({ promoCode, checkoutConsumerPage });
      } else if (expectedAmountToPay > 0 && !this.actor.get.creditCard) {
        throw new Error('Cannot drop packages without a promo code or credit card to pay with');
      }
    }

    if (doesSiteAllowDelivery) {
      await this.addAddress({
        address: this.actor.address,
        additionalContactInfo,
        checkoutConsumerPage,
      });

      if (this.actor.dropType === DropTypesEnum.Delivery) {
        await this.selectDropitPass({ checkoutPage: checkoutConsumerPage });
      }
    } else if (this.actor.site.amountOfCollectionPoints > 1) {
      await this.selectCollectionPoint();
    }

    if (shouldUseImages) {
      await this.addReceiptsWithImage({ checkoutPage: checkoutConsumerPage });
    } else {
      await this.addReceiptsManually({ money, checkoutPage: checkoutConsumerPage });
    }

    const addedPackages = await this.addPackagesToDrop({
      additionalPackages: packages,
      checkoutConsumerPage,
      shouldPackageAdditionSucceed: shouldPackageAdditionSucceed,
      shouldUseImages,
    });

    if (!shouldPackageAdditionSucceed) {
      return;
    }

    if (this.actor.get.isFirstDrop) {
      const checkoutTotalText = await checkoutConsumerPage.checkoutTotal.textContent();
      if (!checkoutTotalText) {
        throw new Error('Failed to get checkout total');
      }

      if (expectedAmountToPay == 0 && this.actor.get.creditCard) {
        expectedAmountToPay = DataManager.DataConverter.convertTextToNumber(checkoutTotalText);
        Logger.debug(`Set expected amount to pay to ${expectedAmountToPay}`);
      } else if (expectedAmountToPay > 0) {
        // Seems like there is a minor bug:
        // The format of the checkout total is not in the correct language local
        // and every param has actually a different format, if: [BRAND-4263](https://dropit.atlassian.net/browse/BRAND-4263) Gets Fixed
        // remove this comment and uncomment the following lines
        // const expectedAmountToPayString = DataManager.DataConverter.convertNumberToPaymentString({
        //   amount: expectedAmountToPay,
        //   currencyCode: this.actor.site.currencyCode,
        //   languageLocal: await this.actor.ask.whatIsPageLanguage(),
        // });

        expect(checkoutTotalText).toContain(expectedAmountToPay.toString());
      }
    }

    if (shouldAssertBeforeCheckout) {
      await this.actor.assert.deliveryDetailsBeforeCheckout({
        assertion: (deliveryDetails) => {
          expect.soft(deliveryDetails.numberOfReceipts, 'Asserting Number of receipts').toBe(1);
          expect
            .soft(deliveryDetails.numberOfPackages, 'Asserting packages number to be as expected')
            .toBe(addedPackages.length);
          expect
            .soft(deliveryDetails.scannedPackageCodes, 'Asserting Scanned package codes length to be as expected')
            .toHaveLength(addedPackages.length);
          for (const [i, packagee] of addedPackages.entries()) {
            expect
              .soft(deliveryDetails.scannedPackageCodes[i], 'Asserting Scanned package codes values and order')
              .toContain(packagee.packageCode);
          }
        },
      });
    }

    if (shouldCheckout) {
      await this.checkout(checkoutConsumerPage, expectedAmountToPay, failDrop);
    }
  }

  @TestStep(`Select Dropit pass`)
  public async selectDropitPass({ checkoutPage }: { checkoutPage: CheckoutConsumerPage }) {
    await checkoutPage.deliveryOptionsMenuOptionButton.click();
    await checkoutPage.defaultDeliveryPassOption.click();
    await checkoutPage.deliveryOptionsSaveButton.click();
  }

  @TestStep(`Confirms cutoff time popup`)
  public async confirmCutoffTimePopup({ checkoutConsumerPage, expectedToBeVisible = true }) {
    if (!this.actor.get.isFirstDrop) {
      return;
    }

    if (expectedToBeVisible) {
      await expect(checkoutConsumerPage.gotItButton.locator).toBeVisible({ timeout: 20000 });
    }

    if (await checkoutConsumerPage.gotItButton.isVisible()) {
      await checkoutConsumerPage.gotItButton.click();
    }
  }
}
