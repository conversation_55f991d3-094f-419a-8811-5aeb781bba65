import { faker } from '@faker-js/faker';
import { v4 as uuidv4 } from 'uuid';
import DataManager from '../../dataManager';
import Package from '../../entities/package';
import Site from '../../entities/site';

export default class DataGenerator {
  todayDate() {
    const date = new Date();
    const formattedDate = new Intl.DateTimeFormat('he-IL').format(date);

    return formattedDate;
  }

  generateInvalidPhoneNumbers(badChars = '!+#%^&*()e,-$%') {
    const validPhoneNumber = DataManager.Consts.PHONE_NUMBER1;
    const badPhoneNumbers: string[] = [
      '000000000',
      '123456789',
      '1',
      '!+#$%^&*()',
      '054e138339',
      `00${DataManager.Consts.PHONE_NUMBER1}`,
      `${DataManager.Consts.PHONE_NUMBER1}9`,
    ];

    for (const badChar of badChars) {
      const randomDigitIndex = Math.floor(Math.random() * validPhoneNumber.length);
      badPhoneNumbers.push(
        validPhoneNumber.slice(0, randomDigitIndex) + badChar + validPhoneNumber.slice(randomDigitIndex + 1),
      );
    }

    return badPhoneNumbers;
  }

  generateInvalidEmail() {
    return `${faker.person.firstName().toLocaleLowerCase()}${faker.person.lastName().toLocaleLowerCase()}@dropitshopping.com`;
  }

  generateUniqueId() {
    return uuidv4();
  }

  generateInvalidUSPhoneNumbers() {
    const invalidAreaCodes = ['000', '123', '555', '999'];
    return invalidAreaCodes.map((code) => `${code} 123 4567`);
  }

  generateInvalidUKPhoneNumbers() {
    const invalidAreaCodes = ['0000', '0111', '0222', '0999'];
    return invalidAreaCodes.map((code) => `${code} 123456`);
  }

  generateFakePhoneNumber() {
    const prefix = faker.helpers.arrayElement(['052', '053', '054', '055', '058', '059']);
    const number = faker.string.numeric(7);

    return `${prefix}${number}`;
  }

  generateFirstNameAndLastName({ shouldLowerCase = true, singularString = false } = {}) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    let output;

    if (!shouldLowerCase) {
      output = { firstName: firstName, lastName: lastName };

      return !singularString ? output : output.firstName + output.lastName;
    }

    output = { firstName: firstName.toLocaleLowerCase(), lastName: lastName.toLocaleLowerCase() };

    return !singularString ? output : output.firstName + output.lastName;
  }

  generatePackages(site: Site, count: number, packageCodeLength = 12) {
    const packages: Package[] = [];
    for (let i = 0; i < count; i++) {
      packages.push(new Package(this.generatePackageCode(site, packageCodeLength)));
    }

    return packages;
  }

  generatePackageCode(site: Site, packageCodeLength = 12) {
    return `${site.packageCodePrefix}${this.generateRandomBarcode(packageCodeLength)}`;
  }

  generateInvalidPackageCodes(site: Site) {
    return [
      new Package('123456789'),
      new Package(`${site.packageCodePrefix}123`),
      new Package(`${site.packageCodePrefix}9090909090809809809890`),
    ];
  }

  // This can create duplicate barcodes, but in very low likelihood
  // around 1/10^12 chance which is extremely unlikely
  private generateRandomBarcode(length: number): string {
    return Math.floor(10 ** (length - 1) + Math.random() * 10 ** (length - 1)).toString();
  }
}
