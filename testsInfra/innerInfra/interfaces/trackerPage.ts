import DeliveryTrackerStepStatusEnum from '../../enums/deliveryTrackerStepStatusEnum';
import Button from '../pages/elements/button';
import ItemsList from '../pages/elements/searchResults';
import TextArea from '../pages/elements/textArea';

export interface TrackerPage {
  searchInput: TextArea;
  searchResults: ItemsList;
  onTheWayStepTitle: Button;
  getStepStatus: (stepName: string) => Promise<DeliveryTrackerStepStatusEnum | null>;
}
