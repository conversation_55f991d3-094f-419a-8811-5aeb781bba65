import { expect, Locator } from '@playwright/test';
import { Logger } from '../../logger';

export default abstract class BaseElement {
  public readonly locator: Locator;

  constructor(locator: Locator) {
    this.locator = locator;
  }

  async isVisible(): Promise<boolean> {
    return await this.locator.isVisible();
  }

  async waitToBeVisible({
    timeout = 5000,
    shouldThrowError = false,
    shouldThrowSoftError = false,
    errorMessage = undefined,
  }: {
    timeout?: number;
    shouldThrowError?: boolean;
    shouldThrowSoftError?: boolean;
    errorMessage?: string;
  } = {}) {
    try {
      await expect(this.locator).toBeVisible({ timeout });
      await expect(this.locator).toBeAttached({ timeout });
      await expect(this.locator).toBeEnabled({ timeout });

      return true;
    } catch (error) {
      this.handleVisibilityError({
        shouldThrow: shouldThrowError,
        error,
        shouldThrowSoftError,
        customMessage: errorMessage,
      });

      return false;
    }
  }

  async waitToBeInvisible({
    timeout = 5000,
    shouldThrowError = false,
    shouldThrowSoftError = false,
    errorMessage = undefined,
  }: {
    timeout?: number;
    shouldThrowError?: boolean;
    shouldThrowSoftError?: boolean;
    errorMessage?: string;
  } = {}) {
    try {
      await expect(this.locator).toBeHidden({ timeout });

      return true;
    } catch (error) {
      this.handleVisibilityError({
        shouldThrow: shouldThrowError,
        error,
        shouldThrowSoftError,
        customMessage: errorMessage,
      });

      return false;
    }
  }

  async click({
    timeout = 45000,
    shouldScrollIntoView = false,
    trial = false,
    reclickCondition = undefined,
  }: {
    timeout?: number;
    shouldScrollIntoView?: boolean;
    trial?: boolean;
    reclickCondition?: () => Promise<boolean>;
  } = {}) {
    if (shouldScrollIntoView) {
      await this.scrollIntoView();
    }

    await this.locator.click({ timeout, trial });

    if (reclickCondition) {
      if (await reclickCondition()) {
        await this.locator.click({ timeout, trial });
      }
    }
  }

  async fill(value: string) {
    await this.click();
    await this.locator.fill(value);
  }

  async textContent({
    shouldClick = true,
    shouldCleanTextContent = false,
    throwErrorIfEmpty = true,
    timeout = 5000,
  } = {}) {
    if (shouldClick) {
      Logger.debug('Clicking on element before getting text content');
      await this.click();
    }

    try {
      await this.waitToBeVisible({ timeout });
      Logger.debug('Element is visible, proceeding to get text content');
    } catch (error) {
      if (!throwErrorIfEmpty) {
        Logger.error(`Failed to find element: ${error.message}`);

        return '';
      }

      throw error;
    }

    const text = await this.locator.textContent();

    if (shouldCleanTextContent && text) {
      return text.replace(/\s+/g, ' ').trim();
    }

    if (!text) {
      if (throwErrorIfEmpty) {
        expect(false, 'Element has no text content, either a bug or it was used on the wrong element').toBeTruthy();
      }

      return '';
    }

    return text;
  }

  async scrollIntoView() {
    await this.locator.scrollIntoViewIfNeeded();
  }

  async getBorderColor() {
    return await this.locator.evaluate((el) => {
      return getComputedStyle(el).borderColor;
    });
  }

  async press(key: string) {
    await this.locator.press(key);
  }

  private handleVisibilityError({
    shouldThrow,
    error,
    shouldThrowSoftError,
    customMessage,
  }: {
    shouldThrow: boolean;
    error: Error;
    shouldThrowSoftError: boolean;
    customMessage?: string;
  }) {
    if (customMessage) {
      Logger.error(`Custom message: ${customMessage}`);
      Logger.debug(`Original error: ${error.message}`);
    } else {
      Logger.error(error.message);
    }

    if (shouldThrow) {
      if (shouldThrowSoftError) {
        expect.soft(false, customMessage ?? error.message).toBeTruthy();
      } else {
        expect(false, customMessage ?? error.message).toBeTruthy();
      }
    }
  }
}
