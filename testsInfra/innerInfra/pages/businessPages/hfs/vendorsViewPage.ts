import { Page } from '@playwright/test';
import Button from '../../elements/button';
import DropdownListWithSearch from '../../elements/dropDownlistWithSearch';
import ItemsList from '../../elements/searchResults';
import TextArea from '../../elements/textArea';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class VendorsViewPage extends NavigationBusinessPage {
  readonly vendorsCards: ItemsList<Button>;
  readonly searchBar: TextArea;
  readonly groupDropDown: DropdownListWithSearch;
  readonly addVendorButton: Button;

  constructor(page: Page) {
    super(page);
    this.vendorsCards = new ItemsList(page.getByTestId('VendorCard.StyledCard'), Button);
    this.searchBar = new TextArea(page.getByTestId('SearchInput.Paper.InputBase'));
    this.groupDropDown = new DropdownListWithSearch(
      page.getByTestId('SingleSelectMenuButton'),
      page.locator("[data-testid^='singleSelectMenuItemContainer'] li"),
    );
    this.addVendorButton = new Button(page.getByTestId('addVendorButton'));
  }

  public override getInitialUrl() {
    return `${super.getInitialUrl()}vendors`;
  }
}
