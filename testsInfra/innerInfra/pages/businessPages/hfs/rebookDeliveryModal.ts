import { Page } from '@playwright/test';
import BasePage from '../../basePage';
import Button from '../../elements/button';
import ItemsList from '../../elements/searchResults';
import TextArea from '../../elements/textArea';

export default class RebookDeliveryModal extends BasePage {
  readonly rebookDeliveryButton: Button;
  readonly reasonOfChangeTextArea: TextArea;
  readonly couriersOptions: ItemsList<Button>;
  readonly cancelButton: Button;

  constructor(page: Page, url?: string) {
    super(page, url);
    this.rebookDeliveryButton = new Button(page.getByRole('button', { name: 'Rebook' }));
    this.reasonOfChangeTextArea = new TextArea(page.getByRole('textbox', { name: 'Write your comment here please' }));
    this.couriersOptions = new ItemsList(page.locator('.PrivateSwitchBase-input'), Button);
    this.cancelButton = new Button(page.getByRole('button', { name: 'Cancel' }));
  }
}
