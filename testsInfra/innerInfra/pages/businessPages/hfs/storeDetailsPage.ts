import { Page } from '@playwright/test';
import Button from '../../elements/button';
import DropdownList from '../../elements/dropDownlist';
import ItemsList from '../../elements/searchResults';
import TextArea from '../../elements/textArea';
import VisualCue from '../../elements/visualCue';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class StoreDetailsPage extends NavigationBusinessPage {
  readonly infoButton: Button;
  readonly fulfillmentAppButton: Button;
  readonly dropPointsButton: Button;
  readonly storeTypeDropDownInfoTab: DropdownList;
  readonly passwordFulfillmentAppTab: TextArea;
  readonly storeQRCodeDropPointTab: ItemsList<VisualCue>;

  constructor(page: Page) {
    super(page);

    const commonButtonSelector = 'StoreDetailsTabsDropitAdmin.Tabs.Tab';
    this.infoButton = new Button(page.getByTestId(commonButtonSelector));
    this.fulfillmentAppButton = new Button(page.getByTestId(commonButtonSelector + '2'));
    this.dropPointsButton = new Button(page.getByTestId(commonButtonSelector + '3'));
    this.storeTypeDropDownInfoTab = new DropdownList(
      page.getByTestId('StoreDetailsInfo.StandardCard.CustomBox.StandardSelect'),
      page.getByTestId('StandardSelect.Select.MenuItem.ListItemText'),
    );
    this.passwordFulfillmentAppTab = new TextArea(page.getByTestId('StandardTextField.StyledTextField'));
    this.storeQRCodeDropPointTab = new ItemsList(page.getByTestId('StoreDetailsDropPoints.StandardCard'), VisualCue);
  }

  public override getInitialUrl() {
    return `${super.getInitialUrl()}stores/`;
  }
}
