import { Page } from '@playwright/test';
import Button from '../../elements/button';
import DropdownList from '../../elements/dropDownlist';
import ItemsList from '../../elements/searchResults';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class StoresViewPage extends NavigationBusinessPage {
  readonly siteDropDownFilter: DropdownList;
  readonly vendorDropDownFilter: DropdownList;
  readonly groupDropDownFilter: DropdownList;
  readonly locationDropDownFilter: DropdownList;

  readonly groupResultsByDropDown: DropdownList;
  readonly storeCards: ItemsList<Button>;

  constructor(page: Page) {
    super(page);

    const commonFilterSelector = 'StoresGlobalView.CustomBox.div.CustomBox.StandardSelect';
    const menuItemSelector = 'StandardSelect.Select.MenuItem';

    this.siteDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector),
      page.getByTestId(menuItemSelector),
    );

    this.vendorDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector + '2'),
      page.getByTestId(menuItemSelector),
    );

    this.locationDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector + '3'),
      page.getByTestId(menuItemSelector),
    );

    this.groupDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector + '4'),
      page.getByTestId(menuItemSelector),
    );

    this.groupResultsByDropDown = new DropdownList(
      page.getByTestId(commonFilterSelector + '5'),
      page.getByTestId(menuItemSelector),
    );

    this.storeCards = new ItemsList(page.getByTestId('StoreInfoCard'), Button);
  }

  public override getInitialUrl() {
    return `${super.getInitialUrl()}stores`;
  }
}
