import { Page } from '@playwright/test';
import DeliveryTrackerStepStatusEnum from '../../enums/deliveryTrackerStepStatusEnum';
import { TrackerPage } from '../interfaces/trackerPage';
import BasePage from './basePage';
import Button from './elements/button';
import TrackerCardElement from './elements/fulfillmentCardElement';
import ItemsList from './elements/searchResults';
import TextArea from './elements/textArea';

export class BaseTrackerPage extends BasePage implements TrackerPage {
  readonly searchInput: TextArea;
  readonly searchResults: ItemsList;
  readonly onTheWayStepTitle: Button;

  constructor(page: Page) {
    super(page);

    this.searchInput = new TextArea(page.getByTestId('trackerListSearchTextInput'));
    this.searchResults = new ItemsList<TrackerCardElement>(
      page.locator('[data-testid^="trackerListCardButton"]'),
      TrackerCardElement,
    );
    this.onTheWayStepTitle = new Button(page.getByTestId('On The Way-Step-title'));
  }

  async getStepStatus(stepName: string): Promise<DeliveryTrackerStepStatusEnum | null> {
    const stepElement = this.page.getByTestId(`${stepName}-Step-title`);
    const logoElement = stepElement.locator('[data-testid^="IconStepperStep"]');

    const dataTestId = await logoElement.getAttribute('data-testid');
    const status = dataTestId as DeliveryTrackerStepStatusEnum;

    if (!dataTestId || !status) {
      throw new Error(`There is no data-testid for the step ${stepName} or it was changed`);
    }

    return status;
  }
}
