import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import Button from '../elements/button';
import ScanPackageInput from '../elements/scanPackageInput';
import ItemsList from '../elements/searchResults';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';
import BaseFulfillmentPage from './baseFulfillmentPage';

export default class DispatchFulfillmentPage extends BaseFulfillmentPage {
  readonly dispatchSearchInput: TextArea;
  readonly dispatchListDeliveryResults: ItemsList;
  readonly scanPackagesInput: TextArea;
  readonly dispatchNameInput: TextArea;
  readonly dispatchSubmitButton: Button;
  readonly dispatchOkButton: Button;
  readonly dispatchedPackages: ItemsList;

  constructor(page: Page) {
    super(page);

    this.dispatchSearchInput = new TextArea(page.getByTestId('dispatchSearchTextInput'));
    this.dispatchListDeliveryResults = new ItemsList(page.locator('[data-testid^="dispatchListCardButton"]'), Button);

    this.scanPackagesInput = new ScanPackageInput({ page });
    this.dispatchNameInput = new TextArea(page.getByTestId('dispatchNameInputText'));
    this.dispatchSubmitButton = new Button(page.getByTestId('dispatchSubmitButton'));
    this.dispatchOkButton = new Button(page.getByTestId('funModalDispatchOkButton'));
    this.dispatchedPackages = new ItemsList(
      page.locator('[data-testid*="dispatchDeliveryDetailsInfoPackagesData"]'),
      VisualCue,
    );
  }

  public override getInitialUrl() {
    return `https://fulfillment-app.${DataManager.Consts.TESTING_ENV}.drpt.io/dispatch`;
  }
}
