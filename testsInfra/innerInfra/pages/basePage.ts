import { Page } from '@playwright/test';
import DataManager from '../../dataManager';
import { TestStep } from '../decorators';
import { Logger } from '../logger';

declare global {
  interface Window {
    verificationCallback: (response: string) => void;
    errorCallback: () => void;
  }
}

export default abstract class BasePage {
  public readonly page: Page;
  protected url: string;
  private readonly intercomPanelName = 'intercom-notifications-frame';

  constructor(page: Page, url?: string) {
    this.page = page;
    this.url = url ?? this.getInitialUrl();
  }

  public getInitialUrl() {
    return this.url;
  }

  getCurrentUrl() {
    return this.page.url();
  }

  protected addEventListeners() {
    this.page.on('frameattached', async (frame) => {
      if (frame.name() === this.intercomPanelName) {
        await this.removeIntercomNotificationPopup();
      }
    });

    this.page.on('framenavigated', async (frame) => {
      if (frame.name() === this.intercomPanelName) {
        await this.removeIntercomNotificationPopup();
      }
    });
  }

  private async setupBlockersAndEventListeners() {
    await this.addEventListeners();
    await this.blockPrintPopup();
  }

  async navigate(url?: string) {
    await this.setupBlockersAndEventListeners();

    await this.page.goto(url ?? this.url);
  }

  async refreshPage() {
    await this.page.reload();
    await this.setupBlockersAndEventListeners();
  }

  async removeCaptcha() {
    await this.page.evaluate(() => {
      if (window.verificationCallback) {
        window.verificationCallback('let me in');
      } else if (window.errorCallback) {
        window.errorCallback();
      }
    });
  }

  @TestStep(`Block print popup`)
  async blockPrintPopup() {
    const printPopupRoutes = this.getPrintPopupRoutes();

    for (const route of printPopupRoutes) {
      await this.page.context().route(route, async (route) => {
        Logger.info(
          `Blocking print popup, Intercepted request:\n${route.request().url()}\nIf you wish to view the file, go to the url`,
          route.request().url(),
        );

        await route.abort();
      });
    }
  }

  private getPrintPopupRoutes() {
    const routeSuffix = '/**';
    const routePrefix = '**/cdn/**/';

    return [`${routePrefix}sorting-labels${routeSuffix}`, `${routePrefix}dispatch-labels${routeSuffix}`];
  }

  async removeIntercomNotificationPopup() {
    const intercomPopup = this.page.locator(`iframe[name="${this.intercomPanelName}"]`);

    try {
      await intercomPopup.evaluate((el) => el.remove());
      Logger.info('Removed Intercom Notification Popup');
    } catch (error) {
      Logger.warning('Ignoring error when removing Intercom widget', error.message);
    }
  }

  async waitForLoad({ shouldWaitForEntirePage = true } = {}) {
    if (shouldWaitForEntirePage) {
      await this.page.waitForURL(this.getUrlPattern(), { timeout: DataManager.Consts.LONG_ACTION_TIMEOUT });
    }

    await this.waitForPageElements();
  }

  protected getUrlPattern(): RegExp | string {
    return /.*/;
  }

  protected async waitForPageElements() {
    await this.page.waitForLoadState('load');
  }
}
