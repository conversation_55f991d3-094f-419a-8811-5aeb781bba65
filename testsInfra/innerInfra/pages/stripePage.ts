import { Page, expect } from '@playwright/test';
import BasePage from './basePage';
import Button from './elements/button';
import TextArea from './elements/textArea';
import VisualCue from './elements/visualCue';

export default class StripePage extends BasePage {
  readonly orderSummaryColumn: VisualCue;
  readonly cardNumberInput: TextArea;
  readonly cardExpiryInput: TextArea;
  readonly cardCvcInput: TextArea;
  readonly billingName: TextArea;
  private _billingPostalCodeInput: TextArea;
  readonly submitButton: Button;
  readonly cardDetailsExpandButton: Button;
  readonly backToDropitButton: Button;
  readonly totalPaymentAmount: (expectedAmount: string) => VisualCue;

  constructor(page: Page) {
    super(page);

    this.orderSummaryColumn = new VisualCue(page.getByTestId('order-summary-column'));
    this.cardNumberInput = new TextArea(page.locator('#cardNumber'));
    this.cardExpiryInput = new TextArea(page.locator('#cardExpiry'));
    this.cardCvcInput = new TextArea(page.locator('#cardCvc'));
    this.billingName = new TextArea(page.locator('#billingName'));
    this._billingPostalCodeInput = new TextArea(page.locator('#billingPostalCode'));
    this.submitButton = new Button(page.getByTestId('hosted-payment-submit-button'));
    this.cardDetailsExpandButton = new Button(page.getByTestId('card-accordion-item'));
    this.backToDropitButton = new Button(page.getByRole('link', { name: 'Back to Dropit Shopping' }));
    this.totalPaymentAmount = (expectedAmount: string) => {
      return new VisualCue(this.page.getByText(expectedAmount));
    };
  }

  protected override getUrlPattern(): RegExp {
    return /checkout.stripe.com/;
  }

  protected override async waitForPageElements() {
    await expect(this.orderSummaryColumn.locator).toBeVisible();
  }

  // Stripe can add and remove this element, so we need to check if it exists
  public async GetBillingPostalCodeInput() {
    if (await this._billingPostalCodeInput.isVisible()) {
      return this._billingPostalCodeInput;
    }

    return undefined;
  }

  public override getInitialUrl() {
    return 'https://checkout.stripe.com';
  }
}
