import { expect } from '@playwright/test';
import AdminUser from '../../testsInfra/actors/admin';
import { BusinessOwnerUser } from '../../testsInfra/actors/businessOwner';
import { ConsumerUser } from '../../testsInfra/actors/consumer';
import DataManager from '../../testsInfra/dataManager';
import { test } from '../baseTest';

const allSites = Object.values(DataManager.Consts.Sites);

test.describe('Pages Health Check, see that all pages render without errors', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Consumer App',
      ticketNumber: 102,
    }),
    {
      tag: [
        DataManager.Consts.Tags.STAGING_TAG,
        DataManager.Consts.Tags.SMOKE_TAG,
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }, testInfo) => {
      const consumerWithNoDrops = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER2,
        DataManager.Consts.Sites.CollectionOnlySite,
      );
      const newConsumer = new ConsumerUser(
        mobilePage,
        'Solomon Reed',
        DataManager.DataGenerator.generateFakePhoneNumber(),
        DataManager.Consts.Sites.CollectionOnlySite,
      );
      const consumerWithExistingDrops = new ConsumerUser(
        mobilePage,
        'Automation Dropper',
        DataManager.Consts.CONSUMER_NUMBER_WITH_DROPS,
        DataManager.Consts.Sites.CollectionOnlySite,
      );

      await newConsumer.goTo.registerPage();

      // Less common flow, scanning through app, the more common flow we have a specific test for
      await consumerWithNoDrops.perform.login();
      await consumerWithNoDrops.goTo.checkoutPage({ shouldScanQRCodeThroughApp: true });
      await consumerWithNoDrops.goTo.trackerPage();
      await consumerWithNoDrops.goTo.settingsPage();
      await consumerWithNoDrops.goTo.intercomModal({ shouldLogin: false });
      await consumerWithNoDrops.goTo.homePage({ fromIntercomModal: true });
      await consumerWithNoDrops.perform.logout();

      await consumerWithExistingDrops.perform.login({
        country: DataManager.Consts.CONSUMER_NUMBER_WITH_DROPS_COUNTRY,
      });
      await consumerWithExistingDrops.goTo.trackerPage();

      expect(
        testInfo.errors,
        'Asserting Consumer App Pages rendered without errors, view above errors for more detail',
      ).toHaveLength(0);
    },
  );

  test.describe('Business App', () => {
    for (const site of allSites) {
      test(
        DataManager.DataConverter.convertToTestTitleName({
          titleText: `Business App for ${site.name}`,
          ticketNumber: 96,
        }),
        {
          tag: [
            DataManager.Consts.Tags.DEV_TAG,
            DataManager.Consts.Tags.QA_TAG,
            DataManager.Consts.Tags.STAGING_TAG,
            DataManager.Consts.Tags.SMOKE_TAG,
            DataManager.Consts.Tags.BUSINESS_APP_TAG,
          ],
        },
        async ({ smallPcPage }, testInfo) => {
          const admin = new AdminUser(smallPcPage);

          await admin.perform.login();
          await admin.goTo.operationsHubPage({ site, afterLogin: true });
          await admin.goTo.pickupsPage();
          await admin.goTo.deliveriesPage();
          // We removed view vendor details because it's not working properly and it's not prioritized for a fix
          // When this bug is fixed this line can be uncommented and replace the current one
          // await admin.goTo.vendorsViewPage({ viewVendorDetails: true });
          await admin.goTo.vendorsViewPage();
          await admin.goTo.storesViewPage({ viewRandomStoreDetails: true });
          await admin.goTo.intercomModal({ shouldLogin: false });

          expect(
            testInfo.errors,
            'Asserting Business App Pages rendered without errors, view above errors for more detail',
          ).toHaveLength(0);
        },
      );
    }
  });

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `Fulfillment App`,
      ticketNumber: 103,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.STAGING_TAG,
        DataManager.Consts.Tags.SMOKE_TAG,
        DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
      ],
    },
    async ({ page }, testInfo) => {
      const bizUser = new BusinessOwnerUser(page, DataManager.Consts.Sites.BusinessDropSite);

      await bizUser.perform.login();
      await bizUser.perform.showFulfillmentUI();
      await bizUser.goTo.deliveryPage();
      await bizUser.goTo.dispatchPage();
      await bizUser.goTo.trackOrdersPage();
      await bizUser.goTo.homePage();
      await bizUser.goTo.trackOrdersPage({ shouldNavigateThroughMenu: false });
      await bizUser.goTo.intercomModal({ shouldLogin: false });

      expect(
        testInfo.errors,
        'Asserting Fulfillment App Pages rendered without errors, view above errors for more detail',
      ).toHaveLength(0);
    },
  );
});
