import { expect } from '@playwright/test';
import AdminUser from '../testsInfra/actors/admin';
import { BusinessOwnerUser } from '../testsInfra/actors/businessOwner';
import DataManager from '../testsInfra/dataManager';
import DeliveryStatusEnum from '../testsInfra/enums/deliveryStatusEnum';
import { test } from './baseTest';

const assertionTitle = 'Asserting the tracker status';

test.describe('Happy Business Drop Flows in Fulfillment:', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Delivery to home',
      ticketNumber: 44,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
        DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
      ],
    },
    async ({ page, smallPcPage }) => {
      const site = DataManager.Consts.Sites.BusinessDropSite;
      const adminUser = new AdminUser(smallPcPage);
      const businessOwner = new BusinessOwnerUser(page, site);

      const droppedPackages = await businessOwner.perform.droppingAPackageForConsumer();
      await businessOwner.perform.dispatchPackages({ packages: droppedPackages });
      await businessOwner.assert.deliveryStatus({
        packagee: droppedPackages[0],
        assertion: {
          expectFunc: (status, expectedStatus) => expect(status, { message: assertionTitle }).toBe(expectedStatus),
          expectedStatus: DeliveryStatusEnum.OnTheWay,
        },
        shouldUseClickForNavigation: true,
      });

      await adminUser.perform.deliverPackages({
        isLoggedIn: false,
        site: businessOwner.site,
        packagee: droppedPackages[0],
        shouldGoToOperationHub: true,
        shouldGoToOperationHubAfterAction: false,
      });

      await businessOwner.assert.deliveryStatus({
        packagee: droppedPackages[0],
        assertion: {
          expectFunc: (status, expectedStatus) => expect(status, { message: assertionTitle }).toBe(expectedStatus),
          expectedStatus: DeliveryStatusEnum.Delivered,
        },
      });
    },
  );
});
