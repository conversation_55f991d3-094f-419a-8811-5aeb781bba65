import { test as baseTest, BrowserContext, chromium, devices, Page } from '@playwright/test';
import DataManager from '../testsInfra/dataManager';
import HealthCheckApi from '../testsInfra/innerInfra/api/healthCheckApi';
import { Logger } from '../testsInfra/innerInfra/logger';

type Fixtures = {
  smallPcPage: Page;
  midPcPage: Page;
  mobilePage: Page;
  mobilePageWithCameraPermissions: Page;
};

const healthCheckApi = new HealthCheckApi();
const getTestResultMessage = (testInfo) => {
  switch (testInfo.status) {
    case 'passed':
      return '✅ Test Passed';

    case 'failed':
      return '❌ Test Failed';

    case 'skipped':
      return '⏭️ Skipped Test';

    case 'timedOut':
      return '⌛ Test Timed Out';

    case 'interrupted':
      return '⛔ Test Interrupted';

    default:
      return `❔ Unknown Test Result, ${testInfo.status}`;
  }
};

const test = baseTest.extend<Fixtures>({
  smallPcPage: async ({ browser }, use) => {
    const context: BrowserContext = await browser.newContext({
      viewport: { width: 1280, height: 720 },
    });

    const page: Page = await context.newPage();
    await use(page);
    await context.close();
  },

  midPcPage: async ({ browser }, use) => {
    const context: BrowserContext = await browser.newContext({
      viewport: { width: 1920, height: 1080 },
    });

    const page: Page = await context.newPage();
    await use(page);
    await context.close();
  },

  mobilePageWithCameraPermissions: async ({}, use) => {
    const browser = await chromium.launch({
      headless: process.env.CI ? true : false,
      args: ['--disable-web-security', '--use-fake-ui-for-media-stream', '--use-fake-device-for-media-stream'],
    });

    const context = await browser.newContext({
      ...devices['iPhone 15 Pro'],
      permissions: ['camera'],
    });

    const page = await context.newPage();
    await use(page);

    await context.close();
    await browser.close();
  },

  mobilePage: async ({ browser }, use) => {
    const context: BrowserContext = await browser.newContext(devices['iPhone 15 Pro']);

    const page: Page = await context.newPage();

    await use(page);
    await context.close();
  },
});

const getEnvStatus = async () => {
  try {
    await healthCheckApi.verifyAllServicesAreUp();

    return { isEnvUp: true, message: 'Environment is up' };
  } catch (error) {
    Logger.error(error.message);

    return { isEnvUp: false, message: error.message };
  }
};

const wrongTagErrorMessageFormat = (expectedTag) =>
  `Test not tagged with ${expectedTag} but running in ${DataManager.Consts.TESTING_ENV} environment`;

test.beforeEach(async ({ context }, testInfo) => {
  const tags = testInfo.tags;
  const canTestRunInQA = tags?.includes(DataManager.Consts.Tags.QA_TAG);
  const canTestRunInDev = tags?.includes(DataManager.Consts.Tags.DEV_TAG);
  const envStatus = await getEnvStatus();

  Logger.step(
    `\n==================================================================================================\n🧪 Starting test: ${testInfo.title}\n`,
  );

  test.skip(!envStatus.isEnvUp, envStatus.message);

  // Tests will be skipped if any of the next conditions is true
  test.skip(
    !canTestRunInQA && DataManager.Consts.TESTING_ENV === DataManager.Consts.QA_ENV,
    wrongTagErrorMessageFormat(DataManager.Consts.Tags.QA_TAG),
  );

  test.skip(
    !canTestRunInDev && DataManager.Consts.TESTING_ENV === DataManager.Consts.DEV_ENV,
    wrongTagErrorMessageFormat(DataManager.Consts.Tags.DEV_TAG),
  );

  // We are adding this script to the context so that our app recognizes we are in a playwright test
  // And not take the automation into anything it does not need to
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  await context.addInitScript(() => ((window as any).isPlaywrightTest = true));
});

test.afterEach(async ({}, testInfo) => {
  Logger.step(`====================`);
  Logger.step(
    `${getTestResultMessage(testInfo)}: ${testInfo.title}\n==================================================================================================\n`,
  );
});

export { test };
