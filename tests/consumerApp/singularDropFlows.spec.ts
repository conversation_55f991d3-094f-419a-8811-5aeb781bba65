import { expect } from '@playwright/test';
import AdminUser from '../../testsInfra/actors/admin';
import { ConsumerUser } from '../../testsInfra/actors/consumer';
import DataManager from '../../testsInfra/dataManager';
import Package from '../../testsInfra/entities/package';
import DeliveryStatusEnum from '../../testsInfra/enums/deliveryStatusEnum';
import DropTypesEnum from '../../testsInfra/enums/dropTypesEnum';
import { test } from '../baseTest';
import { ConsumerConsts } from './sharedConsts';

const numsOfPackagesToGenerate: number[] = [1, 3];
const shouldSearchPackages = [true, false];

const xPackagesInASingleDeliveryText = (numOfPackagesInDelivery: number, searchPackages: boolean) =>
  `${searchPackages ? 'Admin searches package flow' : 'Admin scans package flow'} ${numOfPackagesInDelivery} packages in a single delivery at`;

test.describe('Happy flows related to barcode/qrcode scanning', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `Happy drop flow with upload receipts images in collection only site with a singular package`,
      ticketNumber: 106,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePageWithCameraPermissions }) => {
      const site = DataManager.Consts.Sites.CollectionOnlySite;
      const consumer = new ConsumerUser(
        mobilePageWithCameraPermissions,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER4,
        site,
        {
          dropType: DropTypesEnum.Collection,
        },
      );

      await consumer.perform.existingConsumerDroppingAPackage({
        packages: [new Package('ITHA942234556160')],
        shouldUseImages: true,
        shouldCheckout: false,
        shouldAssertBeforeCheckout: false,
      });

      await consumer.assert.deliveryDetailsBeforeCheckout({
        assertion: (deliveryDetails) => {
          expect.soft(deliveryDetails.numberOfPackages, 'Asserting Number of packages').toBe(1);
          expect.soft(deliveryDetails.numberOfReceipts, 'Asserting Number of receipts').toBe(1);
          expect.soft(deliveryDetails.scannedPackageCodes, 'Asserting Scanned package codes').toHaveLength(1);
          expect
            .soft(deliveryDetails.scannedPackageCodes[0], 'Asserting Scanned package code')
            .toContain(consumer.droppedPackages[0].packageCode);
          expect.soft(deliveryDetails.droppingLocation, 'Asserting dropping store name').toContain(site.stores[0].name);
        },
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Drop Flow with store scanned outside of app',
      ticketNumber: 104,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage, smallPcPage }) => {
      const consumer = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER1,
        DataManager.Consts.Sites.CollectionOnlySite,
      );
      const admin = new AdminUser(smallPcPage);

      await consumer.perform.existingConsumerDroppingAPackage({
        shouldScanQRCodeThroughApp: false,
      });

      await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
      await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) =>
          expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
      });
    },
  );
});

test.describe('Rebook Delivery', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Rebook Delivery on Delivery waiting for dispatch',
      ticketNumber: 30,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
      ],
    },
    async ({ mobilePage, smallPcPage }) => {
      const consumer = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER4,
        DataManager.Consts.Sites.CollectionAndDeliverySite,
        { dropType: DropTypesEnum.Delivery },
      );

      const admin = new AdminUser(smallPcPage);

      await consumer.perform.existingConsumerDroppingAPackage();
      await admin.perform.loginAndReceivePackages({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.perform.sealPackage({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
      });

      await admin.perform.preparePackageForDispatch({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.assert.courierStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) =>
          expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe('Ready for dispatch'),
      });

      await admin.perform.rebookDelivery({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
        shouldGoToOperationHubAfterAction: false,
      });

      await admin.assert.courierStatus({
        packagee: consumer.droppedPackages[0],
        shouldNavigateToPage: false,
        shouldGoToOperationHubAfterAction: true,
        site: consumer.site,
        assertion: (status) =>
          expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe('Preparing dispatch'),
      });

      await admin.perform.preparePackageForDispatch({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.perform.dispatchDelivery({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.perform.deliverPackages({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
        shouldGoToOperationHubAfterAction: false,
      });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        shouldNavigateToPage: false,
        site: consumer.site,
        assertion: (status) =>
          expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Rebook Delivery on sealed delivery',
      ticketNumber: 30,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
      ],
    },
    async ({ mobilePage, smallPcPage }) => {
      const consumer = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER4,
        DataManager.Consts.Sites.CollectionAndDeliverySite,
        { dropType: DropTypesEnum.Delivery },
      );

      const admin = new AdminUser(smallPcPage);

      await consumer.perform.existingConsumerDroppingAPackage();

      await admin.perform.loginAndReceivePackages({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.perform.sealPackage({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
      });

      await admin.perform.rebookDelivery({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
        shouldGoToOperationHubAfterAction: true,
      });

      await admin.perform.prepareAndDispatchDelivery({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.perform.deliverPackages({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
        shouldGoToOperationHubAfterAction: false,
      });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) =>
          expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
        shouldNavigateToPage: false,
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Rebook Delivery on open,and received delivery',
      ticketNumber: 30,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
      ],
    },
    async ({ mobilePage, smallPcPage }) => {
      const consumer = new ConsumerUser(
        mobilePage,
        'Automation User',
        DataManager.Consts.PHONE_NUMBER4,
        DataManager.Consts.Sites.CollectionAndDeliverySite,
        { dropType: DropTypesEnum.Delivery },
      );

      const admin = new AdminUser(smallPcPage);

      await consumer.perform.existingConsumerDroppingAPackage();

      await admin.perform.login();
      await admin.perform.goToOperationsHubForTheSite({ site: consumer.site, afterLogin: true });

      await admin.perform.rebookDelivery({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
        shouldGoToOperationHubAfterAction: true,
      });

      await admin.perform.receivePackages({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.perform.rebookDelivery({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
        shouldGoToOperationHubAfterAction: true,
      });

      await admin.perform.sealPackage({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
      });

      await admin.perform.prepareAndDispatchDelivery({
        site: consumer.site,
        packages: consumer.droppedPackages,
      });

      await admin.perform.deliverPackages({
        site: consumer.site,
        packagee: consumer.droppedPackages[0],
        shouldGoToOperationHubAfterAction: false,
      });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) =>
          expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
        shouldNavigateToPage: false,
      });
    },
  );
});

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: `Drop Over the Max Amount of packages in a single drop collection only site`,
    ticketNumber: 43,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.CONSUMER_APP_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
      DataManager.Consts.Tags.CONSUMER_DROP_TAG,
    ],
  },
  async ({ mobilePage }) => {
    const site = DataManager.Consts.Sites.CollectionOnlySite;
    const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
      dropType: DropTypesEnum.Collection,
    });

    // Note: The max amount of packages that can be added to a singular drop without the element not appearing anymore
    // is 61, from 61+ the packages will no longer appear in the package list
    // So if the max amount of packages is equal or more than 61 the test will fail
    await consumer.perform.existingConsumerDroppingAPackage({
      packages: DataManager.DataGenerator.generatePackages(
        site,
        DataManager.Consts.MAX_AMOUNT_OF_PACKAGES_IN_A_SINGLE_DROP + 5,
      ),
      failDrop: true,
    });
  },
);

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: `Drop Max Amount of packages in a single drop collection only site`,
    ticketNumber: 43,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.CONSUMER_APP_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
      DataManager.Consts.Tags.CONSUMER_DROP_TAG,
    ],
  },
  async ({ smallPcPage, mobilePage }) => {
    const site = DataManager.Consts.Sites.CollectionOnlySite;
    const admin = new AdminUser(smallPcPage);
    const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
      dropType: DropTypesEnum.Collection,
    });

    // Currently there is a bug where if the package codes are too long the app crashes, hank service specifically
    // if fixed you can get rid of this variable
    // https://dropit.atlassian.net/browse/BACK-2816
    const maxBarcodeSize = 5;

    await consumer.perform.existingConsumerDroppingAPackage({
      packages: DataManager.DataGenerator.generatePackages(
        site,
        DataManager.Consts.MAX_AMOUNT_OF_PACKAGES_IN_A_SINGLE_DROP,
        maxBarcodeSize,
      ),
    });

    await admin.perform.loginAndReceivePackages({
      site: consumer.site,
      packages: consumer.droppedPackages,
    });

    await admin.perform.handoverPackages({
      site: consumer.site,
      packages: consumer.droppedPackages,
    });

    await admin.assert.deliveryStatus({
      packagee: consumer.droppedPackages[0],
      site: consumer.site,
      assertion: (status) =>
        expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
    });
  },
);

test.describe('Happy flow variations in every site', () => {
  for (const shouldSearchPackage of shouldSearchPackages) {
    for (const numOfPackagesToGenerate of numsOfPackagesToGenerate) {
      test.describe(`Collection Only Site `, () => {
        test(
          DataManager.DataConverter.convertToTestTitleName({
            titleText: `${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)}`,
            ticketNumber: numOfPackagesToGenerate === 1 ? 1 : 53,
          }),
          {
            tag: [
              DataManager.Consts.Tags.QA_TAG,
              DataManager.Consts.Tags.DEV_TAG,
              DataManager.Consts.Tags.SANITY_TAG,
              DataManager.Consts.Tags.CONSUMER_APP_TAG,
              DataManager.Consts.Tags.BUSINESS_APP_TAG,
              DataManager.Consts.Tags.CONSUMER_DROP_TAG,
            ],
          },
          async ({ smallPcPage, mobilePage }) => {
            const site = DataManager.Consts.Sites.CollectionOnlySite;
            const admin = new AdminUser(smallPcPage);
            const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
              dropType: DropTypesEnum.Collection,
            });

            await consumer.perform.existingConsumerDroppingAPackage({
              packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
            });

            await admin.perform.loginAndReceivePackages({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.handoverPackages({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.assert.deliveryStatus({
              packagee: consumer.droppedPackages[0],
              site: consumer.site,
              assertion: (status) =>
                expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
            });
          },
        );
      });

      test.describe(`Collection only site with multiple collection points `, () => {
        test(
          DataManager.DataConverter.convertToTestTitleName({
            titleText: `${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)}`,
            ticketNumber: numOfPackagesToGenerate === 1 ? 35 : 109,
          }),
          {
            tag: [
              DataManager.Consts.Tags.QA_TAG,
              DataManager.Consts.Tags.DEV_TAG,
              DataManager.Consts.Tags.SANITY_TAG,
              DataManager.Consts.Tags.CONSUMER_APP_TAG,
              DataManager.Consts.Tags.BUSINESS_APP_TAG,
              DataManager.Consts.Tags.CONSUMER_DROP_TAG,
            ],
          },
          async ({ smallPcPage, mobilePage }) => {
            const site = DataManager.Consts.Sites.CollectionOnlySiteWithMultipleCollectionPoints;
            const admin = new AdminUser(smallPcPage);
            const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
              dropType: DropTypesEnum.Collection,
            });

            await consumer.perform.existingConsumerDroppingAPackage({
              packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
            });

            await admin.perform.loginAndReceivePackages({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.prepareAndDispatchDelivery({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.deliverPackages({
              site: consumer.site,
              packagee: consumer.droppedPackages[0],
              shouldGoToOperationHubAfterAction: false,
            });

            await admin.assert.deliveryStatus({
              packagee: consumer.droppedPackages[0],
              site: consumer.site,
              shouldNavigateToPage: false,
              assertion: (status) =>
                expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
            });
          },
        );
      });

      test.describe(`Delivery Only site `, () => {
        test(
          DataManager.DataConverter.convertToTestTitleName({
            titleText: `Delivery with ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)} Delivery Only site`,
            ticketNumber: numOfPackagesToGenerate === 1 ? 99 : 108,
          }),
          {
            tag: [
              DataManager.Consts.Tags.QA_TAG,
              DataManager.Consts.Tags.DEV_TAG,
              DataManager.Consts.Tags.SANITY_TAG,
              DataManager.Consts.Tags.CONSUMER_APP_TAG,
              DataManager.Consts.Tags.BUSINESS_APP_TAG,
              DataManager.Consts.Tags.CONSUMER_DROP_TAG,
            ],
          },
          async ({ smallPcPage, mobilePage }) => {
            const site = DataManager.Consts.Sites.DeliveryOnlySite;
            const admin = new AdminUser(smallPcPage);
            const consumer = new ConsumerUser(mobilePage, 'Sadfa Asdfa', DataManager.Consts.PHONE_NUMBER4, site, {
              dropType: DropTypesEnum.Delivery,
            });

            await consumer.perform.existingConsumerDroppingAPackage({
              packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
              // Error was made when creating the promotion code in qa
              // All environments should use the same promotion code which is 1234 by default
              // delete this line after sys prep
              promoCode: DataManager.Consts.TESTING_ENV == DataManager.Consts.QA_ENV ? '12345' : '1234',
            });

            await admin.perform.loginAndReceivePackages({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.sealPackage({
              site: consumer.site,
              packagee: consumer.droppedPackages[0],
              shouldGoToOperationHubAfterAction: true,
            });

            await admin.perform.prepareAndDispatchDelivery({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.deliverPackages({
              site: consumer.site,
              packagee: consumer.droppedPackages[0],
              shouldGoToOperationHubAfterAction: false,
            });

            await admin.assert.deliveryStatus({
              packagee: consumer.droppedPackages[0],
              site: consumer.site,
              shouldNavigateToPage: false,
              assertion: (status) =>
                expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
            });
          },
        );
      });

      test.describe(`Collection and Delivery site `, () => {
        test(
          DataManager.DataConverter.convertToTestTitleName({
            titleText: `Collection with ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)}`,
            ticketNumber: numOfPackagesToGenerate === 1 ? 2 : 54,
          }),
          {
            tag: [
              DataManager.Consts.Tags.QA_TAG,
              DataManager.Consts.Tags.DEV_TAG,
              DataManager.Consts.Tags.SANITY_TAG,
              DataManager.Consts.Tags.CONSUMER_APP_TAG,
              DataManager.Consts.Tags.BUSINESS_APP_TAG,
              DataManager.Consts.Tags.CONSUMER_DROP_TAG,
            ],
          },
          async ({ smallPcPage, mobilePage }) => {
            const site = DataManager.Consts.Sites.CollectionAndDeliverySite;
            const admin = new AdminUser(smallPcPage);
            const consumer = new ConsumerUser(mobilePage, 'Itzik Beja', DataManager.Consts.PHONE_NUMBER2, site, {
              dropType: DropTypesEnum.Collection,
            });

            await consumer.perform.existingConsumerDroppingAPackage({
              packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
            });

            await admin.perform.loginAndReceivePackages({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.prepareAndDispatchDelivery({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.deliverPackages({
              site: consumer.site,
              packagee: consumer.droppedPackages[0],
              shouldGoToOperationHubAfterAction: false,
            });

            await admin.assert.deliveryStatus({
              packagee: consumer.droppedPackages[0],
              site: consumer.site,
              assertion: (status) =>
                expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
              shouldNavigateToPage: false,
            });
          },
        );

        test(
          DataManager.DataConverter.convertToTestTitleName({
            titleText: `Delivery with ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)}`,
            ticketNumber: numOfPackagesToGenerate === 1 ? 3 : 55,
          }),
          {
            tag: [
              DataManager.Consts.Tags.QA_TAG,
              DataManager.Consts.Tags.DEV_TAG,
              DataManager.Consts.Tags.SANITY_TAG,
              DataManager.Consts.Tags.CONSUMER_APP_TAG,
              DataManager.Consts.Tags.BUSINESS_APP_TAG,
              DataManager.Consts.Tags.CONSUMER_DROP_TAG,
            ],
          },
          async ({ smallPcPage, mobilePage }) => {
            const site = DataManager.Consts.Sites.CollectionAndDeliverySite;
            const admin = new AdminUser(smallPcPage);
            const consumer = new ConsumerUser(mobilePage, 'Sadfa Asdfa', DataManager.Consts.PHONE_NUMBER3, site, {
              dropType: DropTypesEnum.Delivery,
            });

            await consumer.perform.existingConsumerDroppingAPackage({
              packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
            });

            await admin.perform.loginAndReceivePackages({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.sealPackage({
              site: consumer.site,
              packagee: consumer.droppedPackages[0],
              shouldGoToOperationHubAfterAction: true,
            });

            await admin.perform.prepareAndDispatchDelivery({
              site: consumer.site,
              packages: consumer.droppedPackages,
              shouldSearchPackages: shouldSearchPackage,
            });

            await admin.perform.deliverPackages({
              site: consumer.site,
              packagee: consumer.droppedPackages[0],
              shouldGoToOperationHubAfterAction: false,
            });

            await admin.assert.deliveryStatus({
              packagee: consumer.droppedPackages[0],
              site: consumer.site,
              shouldNavigateToPage: false,
              assertion: (status) =>
                expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
            });
          },
        );
      });
    }
  }
});
