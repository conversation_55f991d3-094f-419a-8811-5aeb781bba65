import { expect } from '@playwright/test';
import AdminUser from '../../testsInfra/actors/admin';
import { ConsumerUser } from '../../testsInfra/actors/consumer';
import DataManager from '../../testsInfra/dataManager';
import CreditCard from '../../testsInfra/entities/creditCard';
import DeliveryStatusEnum from '../../testsInfra/enums/deliveryStatusEnum';
import PromoCouponsCodesEnum from '../../testsInfra/enums/promoCouponsCodesEnum';
import { test } from '../baseTest';
import { ConsumerConsts } from './sharedConsts';

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: `Stripe Full Payment on delivery with 1 package Collection only site`,
    ticketNumber: 47,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.CONSUMER_APP_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
      DataManager.Consts.Tags.STRIPE_TAG,
      DataManager.Consts.Tags.CONSUMER_DROP_TAG,
    ],
  },
  async ({ smallPcPage, mobilePage }) => {
    const site = DataManager.Consts.Sites.CollectionOnlySite;
    const admin = new AdminUser(smallPcPage);
    const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
      creditCard: new CreditCard(),
    });

    await consumer.perform.existingConsumerDroppingAPackage({ promoCode: PromoCouponsCodesEnum.FullPrice });
    await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
    await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

    await admin.assert.deliveryStatus({
      packagee: consumer.droppedPackages[0],
      site: consumer.site,
      assertion: (status) =>
        expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
    });
  },
);

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: `Stripe Partial Payment With Promo Code on delivery with 1 package Collection only site`,
    ticketNumber: 48,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.CONSUMER_APP_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
      DataManager.Consts.Tags.STRIPE_TAG,
      DataManager.Consts.Tags.CONSUMER_DROP_TAG,
    ],
  },
  async ({ smallPcPage, mobilePage }) => {
    const site = DataManager.Consts.Sites.CollectionOnlySite;
    const admin = new AdminUser(smallPcPage);
    const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
      creditCard: new CreditCard(),
    });

    await consumer.perform.existingConsumerDroppingAPackage({
      promoCode: PromoCouponsCodesEnum.HalfPrice,
      expectedAmountToPay: 5,
    });
    await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
    await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

    await admin.assert.deliveryStatus({
      packagee: consumer.droppedPackages[0],
      site: consumer.site,
      assertion: (status) =>
        expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
    });
  },
);

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: `Stripe Failed Payment with 1 package Collection only site`,
    ticketNumber: 80,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.CONSUMER_APP_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
      DataManager.Consts.Tags.STRIPE_TAG,
    ],
  },
  async ({ mobilePage }) => {
    const site = DataManager.Consts.Sites.CollectionOnlySite;
    const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
      creditCard: new CreditCard(),
    });

    await consumer.perform.existingConsumerDroppingAPackage({
      promoCode: PromoCouponsCodesEnum.FullPrice,
      packages: DataManager.DataGenerator.generatePackages(site, 1),
      failDrop: true,
    });

    await test.step('Assert Payment Error', async () => {
      const paymentErrorText = await consumer.ask.whatIsThePaymentErrorText();

      expect(paymentErrorText).toContain('To finalize the drop, please complete your payment.');
    });
  },
);
