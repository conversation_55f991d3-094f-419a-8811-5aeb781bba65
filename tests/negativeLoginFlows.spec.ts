import { expect, Page } from '@playwright/test';
import AdminUser from '../testsInfra/actors/admin';
import { BusinessOwnerUser } from '../testsInfra/actors/businessOwner';
import { ConsumerUser } from '../testsInfra/actors/consumer';
import DataManager from '../testsInfra/dataManager';
import { LoginType } from '../testsInfra/entities/loginType';
import { test } from './baseTest';

const invalidCodes = ['0000', '1!11', '!#$%', 'abcd', 'אאאא', 'atאא'];
const expectedErrorMessageForInvalidInput = 'Wrong email or password';
const expectedEmptyFieldErrorMessage = 'Please fill out this field';
const getAuth0Actor = (loginType: LoginType, page: Page) => {
  return loginType === DataManager.Consts.LoginTypes.Business ? new BusinessOwnerUser(page) : new AdminUser(page);
};
const getTagByLoginType = (loginType: LoginType) => {
  return loginType === DataManager.Consts.LoginTypes.Business
    ? DataManager.Consts.Tags.BUSINESS_APP_TAG
    : DataManager.Consts.Tags.FULFILLMENT_APP_TAG;
};

const loginTypes = Object.keys(DataManager.Consts.LoginTypes);

test.describe('Negative Consumer Login Flows', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Invalid Phone Numbers',
      ticketNumber: 28,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const invalidPhoneNumbers = DataManager.DataGenerator.generateInvalidPhoneNumbers();
      const consumer = new ConsumerUser(mobilePage, 'Bad User', '');
      await consumer.goTo.loginPage();
      await consumer.assert.invalidPhoneNumbers({
        invalidPhoneNumbers: invalidPhoneNumbers,
        assertion: (errorMessage, phoneNumber) =>
          expect
            .soft(errorMessage, {
              message: `Asserting the invalid number: ${phoneNumber}\nif the received string is empty no error message appeared`,
            })
            .toContain('Enter a valid phone number'),
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Invalid US Area codes',
      ticketNumber: 28,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const invalidPhoneNumbers = DataManager.DataGenerator.generateInvalidUSPhoneNumbers();
      const consumer = new ConsumerUser(mobilePage, 'Bad User', '');
      await consumer.goTo.loginPage();
      await consumer.assert.invalidPhoneNumbers({
        invalidPhoneNumbers: invalidPhoneNumbers,
        countryCode: 'United States',
        assertion: (errorMessage, phoneNumber) =>
          expect
            .soft(errorMessage, {
              message: `Asserting the invalid US number: ${phoneNumber}\nIf the received string is empty, no error message appeared.`,
            })
            .toContain('Enter a valid phone number'),
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Invalid UK Area codes',
      ticketNumber: 28,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const invalidPhoneNumbers = DataManager.DataGenerator.generateInvalidUKPhoneNumbers();
      const consumer = new ConsumerUser(mobilePage, 'Bad User', '');
      await consumer.goTo.loginPage();
      await consumer.assert.invalidPhoneNumbers({
        invalidPhoneNumbers: invalidPhoneNumbers,
        countryCode: 'United Kingdom',
        assertion: (errorMessage, phoneNumber) =>
          expect
            .soft(errorMessage, {
              message: `Asserting the invalid UK number: ${phoneNumber}\nIf the received string is empty, no error message appeared.`,
            })
            .toContain('Enter a valid phone number'),
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Invalid Verification Codes',
      ticketNumber: 28,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const consumer = new ConsumerUser(mobilePage, 'Bad User', DataManager.Consts.PHONE_NUMBER1);
      await consumer.goTo.verificationCodePage();
      await consumer.assert.invalidVerificationCodes(invalidCodes, (errorMessage, code) =>
        expect
          .soft(errorMessage, {
            message: `Asserting the invalid code: ${code}\nif the received string is empty no error message appeared`,
          })
          .toContain('Wrong Code!'),
      );
    },
  );
});

test.describe('Negative Admin Login to Auth0Apps Flows', () => {
  for (const loginType of loginTypes) {
    test(
      DataManager.DataConverter.convertToTestTitleName({
        titleText: `${loginType} Empty email & password`,
        ticketNumber: 28,
      }),
      {
        tag: [
          DataManager.Consts.Tags.QA_TAG,
          DataManager.Consts.Tags.DEV_TAG,
          DataManager.Consts.Tags.NEGATIVE_TAG,
          getTagByLoginType(DataManager.Consts.LoginTypes[loginType]),
        ],
      },
      async ({ smallPcPage }) => {
        const emptyString = '';
        const auth0User = getAuth0Actor(DataManager.Consts.LoginTypes[loginType], smallPcPage);
        await auth0User.goTo.loginPage();
        await test.step(`Assert Error Message for empty email`, async () => {
          await auth0User.perform.login({
            email: emptyString,
            password: DataManager.Consts.BUSINESS_ADMIN_PASSWORD,
            shouldNavigateToPage: false,
            expectLoginToSucceed: false,
          });

          const errorMessage = await auth0User.ask.getErrorPopupMessageForEmptyField('email');
          expect
            .soft(errorMessage, {
              message: `Asserting the invalid empty email field`,
            })
            .toContain(expectedEmptyFieldErrorMessage);
        });

        await test.step(`Assert Error Message for empty password`, async () => {
          await auth0User.perform.login({
            email: DataManager.Consts.BUSINESS_ADMIN_EMAIL,
            password: emptyString,
            shouldNavigateToPage: false,
            expectLoginToSucceed: false,
          });

          const errorMessage = await auth0User.ask.getErrorPopupMessageForEmptyField('password');
          expect
            .soft(errorMessage, {
              message: `Asserting the invalid empty password field`,
            })
            .toContain(expectedEmptyFieldErrorMessage);
        });
      },
    );

    test(
      DataManager.DataConverter.convertToTestTitleName({
        titleText: `${loginType} Invalid Credentials Combinations`,
        ticketNumber: 28,
      }),
      {
        tag: [
          DataManager.Consts.Tags.QA_TAG,
          DataManager.Consts.Tags.DEV_TAG,
          DataManager.Consts.Tags.NEGATIVE_TAG,
          DataManager.Consts.Tags.SANITY_TAG,
          getTagByLoginType(DataManager.Consts.LoginTypes[loginType]),
        ],
      },
      async ({ smallPcPage }) => {
        const invalidCredentials = [
          {
            email: DataManager.DataGenerator.generateInvalidEmail(),
            password: DataManager.Consts.BUSINESS_ADMIN_PASSWORD,
          },
          {
            email: DataManager.Consts.BUSINESS_ADMIN_EMAIL,
            password: 'WrongP@ssw0rd',
          },
        ];

        const auth0User = getAuth0Actor(DataManager.Consts.LoginTypes[loginType], smallPcPage);
        await auth0User.goTo.loginPage();
        await auth0User.assert.invalidLoginInputs(invalidCredentials, (errorMessage, credentials) =>
          expect
            .soft(errorMessage, {
              message: `Asserting the invalid combination - Email: ${credentials.email}, Password: ${credentials.password}`,
            })
            .toContain(expectedErrorMessageForInvalidInput),
        );
      },
    );
  }
});

test.describe('Negative Business Owner Login to Fulfillment', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Invalid Verification Codes',
      ticketNumber: 93,
      additionalTickets: [28],
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
      ],
    },
    async ({ smallPcPage }) => {
      const businessOwner = new BusinessOwnerUser(smallPcPage);
      await businessOwner.perform.login();
      await businessOwner.perform.showFulfillmentUI({ shouldInputCode: false });
      await businessOwner.assert.invalidVerificationCodes(invalidCodes, (errorMessage, code) =>
        expect
          .soft(errorMessage, {
            message: `Asserting the invalid code: ${code}\nif the received string is empty no error message appeared`,
          })
          .toContain('Please try again'),
      );
    },
  );
});
